const devConfig = {
  IS_ENABLE_PROFILER: true,
  IS_ENABLE_BILLING: false,
  REQUEST_TIME_OUT_MS: 180000,
  EXPORT_MAX_FILE_SIZE: 50000000,
  CAAS_API_URL: 'http://localhost:8080',
  BI_API_URL: 'http://localhost:8080',
  SCHEMA_API_URL: 'http://localhost:8080',
  DATA_COOK_API_URL: 'http://localhost:8080',
  LAKEHOUSE_API_URL: 'http://localhost:8080/lake',
  CDP_API_URL: 'http://localhost:8080',
  OPEN_API_URL: 'https://api.openai.com',
  BILLING_API_URL: 'http://license.rocket.bi/api/billing',
  JOB_WORKER_API_URL: 'http://localhost:8080',
  JOB_SCHEDULER_API_URL: 'http://localhost:8080',
  RELAY_API_URL: 'http://localhost:8080',
  STATIC_FILE_URL: 'http://staging.rocket.bi:8080/static',

  //upload
  UPLOAD_STATIC_API_URL: 'http://staging.rocket.bi:8080/static',
  STATIC_DOMAIN: 'http://staging.rocket.bi:8080',
  STATIC_MEDIA_PATH: 'static/media',

  //search image
  SHUTTER_ACCESS_TOKEN:
    'v2/cHJ0YXdkQ0NwYW5qNW82MUtnNmVpcGlVaGczY1gzZFkvMzkzNTAwNDY5L2N1c3RvbWVyLzQvazZlTFpRMWx3VWxPVS1wUE53Zk40bnBwYVBzWkFoNFllNkJrQ1J5U0NYTk5mYm5yUVVSaTVManAwTEhZSU96OThFdnE2YVBuYk1MSmVteWNKaWVoeEtlNE00c0d0SnBWOTUweDZoTUd5cGZ5NlBxekVjQ1ZUbGNVOVZSMGg5YWJZOS1PYnNyMUFWMWlmU2V5WFhsZjVOUGxJOFA0dmREVUdOMmkxdzBfUlZ2OG1lc0xueW9tLXd3NjdEVnpCei1nRDFvNmhkVU9tZWljb1JmNUthZS1tZy84VGtsVnE5VTJ2S2xvWlNTNkFZVWhR',

  IS_DISABLE_STREAMING: true,
  IS_DISABLE_LAKE_HOUSE: true,
  IS_DISABLE_CDP: true,
  IS_DISABLE_INGESTION: false,
  IS_DISABLE_USER_ACTIVITIES: false,
  DEFAULT_PASSWORD: 'di@123456',

  SAAS_DOMAIN: 'rocketbi.cf',
  REGISTER_URL: 'https://rocketbi.dev/register',
  //Forgot password config
  TOTAL_PIN_CODE: 6,
  TIMEOUT_SHOW_RESEND_SECOND: 10,

  //Third party ingestion config
  // tiktok
  TIKTOK_ADS_URL: 'https://hello.rocketbi.dev/third-party-auth/tik-tok',
  TIKTOK_APP_ID: '7208807373668876290',
  // shopify
  SHOPIFY_SCOPE:
    'read_analytics, read_customers, read_discounts, read_draft_orders, read_files, read_fulfillments, read_gdpr_data_request, read_gift_cards, read_inventory, read_legal_policies, read_locations, read_marketing_events, read_merchant_managed_fulfillment_orders, read_online_store_pages, read_online_store_navigation, read_order_edits, read_orders, read_payment_terms, read_price_rules, read_product_listings, read_products, read_reports, read_resource_feedbacks, read_script_tags, read_shipping, read_locales, read_markets, read_shopify_payments_accounts, read_shopify_payments_bank_accounts, read_shopify_payments_disputes, read_shopify_payments_payouts, read_content, read_themes, read_third_party_fulfillment_orders, read_translations',
  SHOPIFY_API_VERSION: '2022-04',
  // fb
  FACEBOOK_ADS_URL: 'https://hello.rocketbi.dev/third-party-auth/facebook',
  FACEBOOK_APP_ID: '****************',
  FACEBOOK_SCOPE: 'ads_read',
  // gg
  GOOGLE_CLIENT_ID: '************-r1iu7q1u7hd1rpavp7rd5vokkrlh7f9n.apps.googleusercontent.com',
  GOOGLE_SHEET_URL: 'https://hello.rocketbi.dev/third-party-auth/google-sheet',
  GOOGLE_SHEET_SCOPES: 'https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/spreadsheets.readonly',
  GA_URL: 'https://hello.rocketbi.dev/third-party-auth/google-analytic',
  GA_SCOPES: 'profile https://www.googleapis.com/auth/analytics.readonly',
  GA4_URL: 'https://hello.rocketbi.dev/third-party-auth/ga4',
  GA4_SCOPES: 'profile https://www.googleapis.com/auth/analytics.readonly',
  GOOGLE_SEARCH_CONSOLE_URL: 'https://hello.rocketbi.dev/third-party-auth/google-search-console',
  GOOGLE_SEARCH_CONSOLE_SCOPES: 'https://www.googleapis.com/auth/webmasters.readonly https://www.googleapis.com/auth/webmasters',
  GOOGLE_ADS_URL: 'https://hello.rocketbi.dev/third-party-auth/google-advertise',
  GOOGLE_ADS_SCOPES: 'https://www.googleapis.com/auth/adwords',
  YOUTUBE_URL: 'https://hello.rocketbi.dev/third-party-auth/youtube',
  YOUTUBE_SCOPES: 'https://www.googleapis.com/auth/yt-analytics.readonly',

  TERMS_OF_SERVICE_URL: 'https://rocket.bi/terms-of-service',
  PRIVACY_POLICY_URL: 'https://rocket.bi/privacy-policy',

  RECAPTCHA_SITE_KEY: '6LfHW0UpAAAAAIb2NzUgKD5rOd0bD4VC6hIuB1lY'
};

window.appConfig = devConfig;
