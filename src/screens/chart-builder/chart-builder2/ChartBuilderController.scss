/*!
 * @author: tvc12 - <PERSON>hien Vi
 * @created: 6/3/21, 5:17 PM
 */

@import '~@/themes/scss/mixin.scss';

.data-builder-container {
  display: flex;
  align-items: flex-start;
  height: 100%;
  width: 100%;
  .data-builder-bar {
    width: 134px;
    background: var(--secondary);

    &--menu {
      margin-top: 60px;
      &--item {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        height: 40px;
        cursor: pointer;
        i {
          font-size: 13px;
          margin-right: 10px;
        }
        &:before {
          content: '';
          position: absolute;
          width: 6px;
          left: 0;
          top: 0;
          height: 100%;
        }
      }
      &--item.active,
      &--item:hover {
        background: #e5f4ff;
        color: var(--accent);
      }
      &--item.active:before {
        background: var(--accent);
      }
      &--item + .data-builder-bar--menu--item {
        margin-top: 18px;
      }
    }
  }
}

.data-builder ::v-deep {
  @import '~@/themes/scss/di-variables.scss';
  @import '~bootstrap/scss/variables';
  @import '~@/themes/scss/data-builder/custom/_modal.scss';
  @import '~@/themes/scss/data-builder/custom/_navbar.scss';
  @import '~@/themes/scss/data-builder/custom/_dark-dashboard.scss';
  @import '~@/themes/scss/data-builder/custom/dark-sidebar.scss';
  @import '~@/themes/scss/data-builder/custom/_misc.scss';
  .v-context {
    text-align: center;
  }
}

.data-builder {
  background-color: var(--data-builder-bg, var(--primary));
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 16px;
  padding: 18px 13px 20px 11px;

  > .data-builder-header {
    height: 28px;
    gap: 8px;

    h3 {
      font-size: 24px;
      letter-spacing: 0.2px;
      line-height: 1.4;
      margin-bottom: 0;
    }

    .btn-bar {
      > div {
        padding: 5px 0;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: normal;
        letter-spacing: 0.18px;
        width: 64px;
        color: var(--secondary-text-color);
        //height: 26px;

        &:not(:first-child) {
          margin-left: 16px;
          color: var(--accent-text-color);
        }
      }
    }
  }

  > .data-builder-body {
    flex: 1;
    //?????
    height: 1px;
    //width: calc(100% - 32px);

    //> div + div {
    //  margin-left: 16px;
    //}

    .config-filter-area {
      height: 100%;
      position: relative;

      .config-panel {
        height: 100%;
        @include builder-panel();
        border-radius: 0 4px 4px 0 !important;
        background-color: var(--builder-panel-bg, var(--secondary));
      }
    }

    .visualization-panel {
      flex: 1;
      @include builder-panel();
      background-color: var(--secondary-2, #fafafb);
    }

    .builder-config-container {
      position: relative;
      display: flex;
      margin-right: 8px;

      .setting-panel {
        width: 631px;
        padding: 16px;
        border-radius: 4px;
        background-color: var(--builder-panel-bg, var(--secondary));
      }

      .builder-config {
        display: flex;
        gap: 8px;

        .database-panel {
          @include builder-panel();
          background-color: var(--builder-panel-bg, var(--secondary));
          width: 238px;
          height: 100%;
          border-radius: 4px 0 0 4px;
          ::v-deep {
            .source-item:first-child {
              border-top-left-radius: 4px;
            }
          }
        }

        .config-chart-panel {
          width: 385px;
          height: 100%;
        }
      }
    }
  }
}
