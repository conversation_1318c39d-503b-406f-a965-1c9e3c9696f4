.qkb-bot-bubble {
  align-self: flex-end;
}

.qkb-bubble-btn {
  display: block;
  padding: 0;
  outline: 0;
  border: 0;
  box-shadow: none;
  border-radius: 50%;
  color: $bubble-btn-color;
  fill: $bubble-btn-color;
  cursor: pointer;
  box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.15);
  transition: opacity linear 0.2s;

  &:hover {
    opacity: 0.85;
  }
}

.qkb-bubble-btn-icon {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 27px;
  height: auto;
  margin: -11px 0 0 -13px;
  line-height: 1em;

  &.qkb-bubble-btn-icon--close {
    width: 15px;
    margin: -7px 0 0 -7px;
  }
  &.qkb-bubble-btn-icon--open {
    top: 26px;
    left: 27px;
  }
}
