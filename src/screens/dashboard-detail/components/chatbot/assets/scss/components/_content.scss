.qkb-board-content {
  flex-grow: 1;
  overflow: hidden scroll;
}

.qkb-board-content__bubbles {
  min-height: 100%;
  padding: 0.5rem 1rem;
}

// Message Bubble Components
// TODO: split into single component files
.qkb-msg-bubble {
  display: flex;
  position: relative;
}

.qkb-msg-avatar {
  flex-grow: 1;
  flex: none;
  position: relative;
  overflow: hidden;
  border-radius: 50%;
}

.qkb-msg-avatar__img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  background-size: cover;
  background-repeat: no-repeat;
}

.qkb-msg-bubble__time {
  display: none;
  position: absolute;
  right: 0;
  top: 0;
  padding: 2px 5px;
  margin-top: -5px;
  border-radius: 5px;
  font-size: 0.625rem;
  color: $bubble-time-color;
  background-color: $bubble-time-bg;
  transform: translate(0, -100%);
  opacity: 0;
  transition: opacity linear 0.1s 1s;

  .qkb-msg-bubble--user & {
    display: block;
  }
}

.qkb-msg-bubble-component {
  font-size: 0.875rem;

  &:hover {
    & ~ .qkb-msg-bubble__time {
      opacity: 0.8;
    }
  }
}

.qkb-msg-bubble {
  padding-bottom: 1rem;

  &.qkb-msg-bubble--bot {
    .qkb-msg-bubble-component {
      margin-right: 2.5rem;
      margin-left: 0.5rem;
    }
  }

  &.qkb-msg-bubble--user {
    justify-content: flex-end;

    .qkb-msg-bubble-component {
      margin-left: 5rem;
    }
  }
}

.qkb-msg-bubble-component__text {
  position: relative;
  padding: 0.75rem 1rem;
  border-radius: 6px;
}

.qkb-msg-bubble-component__options-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.qkb-mb-button-options__item {
  flex: 0 0 auto;
}

.qkb-mb-button-options__btn {
  display: block;
  overflow: hidden;
  position: relative;
  margin: 0.5rem 0.375rem 0 0;
  padding: 0.25rem 1rem;
  cursor: pointer;
  outline: 0;
  border: 1px solid transparent;
  border-radius: 13px;
  color: inherit;
  font-size: 0.875rem;
  font-family: inherit;
  text-decoration: none;
  background-color: transparent;
  transition: background-color linear 0.15s, color linear 0.1s;

  span {
    position: relative;
    z-index: 10;
  }
}

// Typing indicator
.qkb-msg-bubble__typing-indicator {
  position: relative;
  min-width: $typing-indicator-size * 3 + 8px;
  opacity: 0.3;

  span {
    display: block;
    width: $typing-indicator-size;
    height: $typing-indicator-size;
    margin: 0 auto;
    border-radius: 50%;
  }

  &::before,
  &::after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    width: $typing-indicator-size;
    height: $typing-indicator-size;
    border-radius: 50%;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }
}
