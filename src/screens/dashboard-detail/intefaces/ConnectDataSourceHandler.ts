// import { Status } from '@/shared';
// import { ListUtils } from '@/utils';
// import { DIException } from '@core/common/domain';
// import { RecurringMethodExecutor } from '@core/common/misc';
// import { DashboardService } from '@core/common/services';
// import { Log } from '@core/utils';
//
// /* eslint-disable @typescript-eslint/no-use-before-define */
// export abstract class ConnectDataSourceHandler {
//   abstract setOnSucess(fn: () => Promise<void>): this;
//   abstract setRefreshIntervalMs(intervalMs: number): this;
//   abstract start(): void;
//   abstract stop(): void;
//   abstract setOnRefresh(fn: (status: Status, results: DataSourceConnectionStatus[]) => void): this;
//   abstract setOnError(fn: (error: DIException) => void): this;
//
//   static createDefault(dashboardService: DashboardService, dashboardId: number): ConnectDataSourceHandler {
//     return new ConnectDataSourceHandlerImpl(dashboardService, dashboardId);
//   }
// }
//
// class ConnectDataSourceHandlerImpl implements ConnectDataSourceHandler {
//   private onSucess?: () => Promise<void>;
//   private onRefresh?: (status: Status, results: DataSourceConnectionStatus[]) => void;
//   private onError?: (ex: DIException) => void;
//   private refreshIntervalMs = 10000;
//   private executor = new RecurringMethodExecutor(this.handleGetDataSourceRefresh.bind(this));
//   private dashboardService: DashboardService;
//   private dashboardId: number;
//
//   constructor(dashboardService: DashboardService, dashboardId: number) {
//     this.dashboardService = dashboardService;
//     this.dashboardId = dashboardId;
//   }
//
//   setOnSucess(fn: () => Promise<void>): this {
//     this.onSucess = fn;
//     return this;
//   }
//
//   setRefreshIntervalMs(intervalMs: number): this {
//     this.refreshIntervalMs = intervalMs;
//     return this;
//   }
//
//   start(): void {
//     this.executor.startImmediately(this.refreshIntervalMs);
//   }
//
//   stop(): void {
//     Log.info('ConnectDataSourceHandlerImpl::stop');
//     this.executor.stop();
//   }
//
//   setOnRefresh(fn: (status: Status, results: DataSourceConnectionStatus[]) => void): this {
//     this.onRefresh = fn;
//     return this;
//   }
//
//   setOnError(fn: (error: DIException) => void): this {
//     this.onError = fn;
//     return this;
//   }
//
//   private async handleGetDataSourceRefresh(): Promise<void> {
//     try {
//       const results = await this.dashboardService.getConnectionStatuses(this.dashboardId);
//       this.executeOnRefresh(results);
//       const status = this.toStatus(results);
//       if (status === Status.Loaded && this.onSucess) {
//         await this.onSucess();
//         this.stop();
//       }
//       if (status === Status.Error) {
//         throw this.getStatusError(results);
//       }
//     } catch (error) {
//       this.stop();
//       const ex = DIException.fromObject(error);
//       if (this.onError) {
//         this.onError(ex);
//       }
//     }
//   }
//
//   private toStatus(statuses: DataSourceConnectionStatus[]): Status {
//     if (this.isSuccess(statuses)) {
//       return Status.Loaded;
//     }
//     if (this.isError(statuses)) {
//       return Status.Error;
//     }
//     return Status.Loading;
//   }
//
//   getStatusError(statuses: DataSourceConnectionStatus[]): DIException {
//     const errors = statuses.filter(status => status.isError());
//     if (ListUtils.isEmpty(errors)) {
//       return new DIException('Unknown error in this dashboard, please refresh the page and try again.');
//     }
//     return ListUtils.getHead(errors)!.getError();
//   }
//
//   private executeOnRefresh(statuses: DataSourceConnectionStatus[]): void {
//     try {
//       if (this.onRefresh) {
//         const status = this.toStatus(statuses);
//         this.onRefresh(status, statuses);
//       }
//     } catch (error) {
//       // ingored
//     }
//   }
//
//   private isSuccess(statuses: DataSourceConnectionStatus[]): boolean {
//     return statuses.every(status => status.isOk());
//   }
//
//   private isError(statuses: DataSourceConnectionStatus[]): boolean {
//     return statuses.some(status => status.isError());
//   }
// }
