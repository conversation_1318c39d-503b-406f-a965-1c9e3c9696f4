<template>
  <div>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
    <p>
      We'll show you how to install the DataInsider Android library using Gradle.<br /><br />
      Check out our Advanced Android Guide for additional configurations and use cases, like setting up your project with European Union data storage.
    </p>
    <h4>Step 1: Install DataInsider</h4>
    <p>You will need your project token for initializing your library. You can get your project token from <a>project settings.</a></p>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({ components: {} })
export default class JsDocument extends Vue {
  //
}
</script>

<style lang="scss"></style>
