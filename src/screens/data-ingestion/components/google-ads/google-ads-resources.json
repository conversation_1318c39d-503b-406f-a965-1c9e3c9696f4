{"v11": {"resources": {"name": [{"id": "ad_group", "displayName": "Ad Group"}, {"id": "ad_group_ad", "displayName": "Ad Group Ad"}, {"id": "ad_group_ad_asset_combination_view", "displayName": "Ad Group Ad Asset Combination View"}, {"id": "ad_group_ad_asset_view", "displayName": "Ad Group Ad Asset View"}, {"id": "ad_group_asset", "displayName": "Ad Group Asset "}, {"id": "ad_group_audience_view", "displayName": "Ad Group Audience View"}, {"id": "ad_schedule_view", "displayName": "Ad Schedule View"}, {"id": "age_range_view", "displayName": "Age Range View"}, {"id": "asset_field_type_view", "displayName": "Asset Field Type View"}, {"id": "asset_group_product_group_view", "displayName": "Asset Group Product Group View"}, {"id": "bidding_strategy", "displayName": "Bidding Strategy"}, {"id": "campaign", "displayName": "Campaign"}, {"id": "campaign_asset", "displayName": "Campaign Asset"}, {"id": "campaign_audience_view", "displayName": "Campaign Audience View"}, {"id": "campaign_bid_modifier", "displayName": "Campaign Bid Modifier"}, {"id": "campaign_budget", "displayName": "Campaign Budget"}, {"id": "campaign_group", "displayName": "Campaign Group"}, {"id": "click_view", "displayName": "Click View"}, {"id": "conversion_action", "displayName": "Conversion Action"}, {"id": "customer", "displayName": "Customer"}, {"id": "customer_asset", "displayName": "Customer <PERSON>"}, {"id": "detail_placement_view", "displayName": "Detail Placement View"}, {"id": "display_keyword_view", "displayName": "Display Keyword View"}, {"id": "distance_view", "displayName": "Distance View"}, {"id": "dynamic_search_ads_search_term_view", "displayName": "Dynamic Search Ads Search Term View"}, {"id": "expanded_landing_page_view", "displayName": "Expanded Landing Page View"}, {"id": "extension_feed_item", "displayName": "Extension Feed Item"}, {"id": "feed_item", "displayName": "<PERSON><PERSON>"}, {"id": "feed_placeholder_view", "displayName": "Feed Placeholder View"}, {"id": "gender_view", "displayName": "Gender View"}, {"id": "geographic_view", "displayName": "Geographic View"}, {"id": "group_placement_view", "displayName": "Group Placement View"}, {"id": "hotel_group_view", "displayName": "Hotel Group View"}, {"id": "hotel_performance_view", "displayName": "Hotel Performance View"}, {"id": "hotel_reconciliation", "displayName": "Hotel Reconciliation"}, {"id": "income_range_view", "displayName": "Income Range View"}, {"id": "keyword_view", "displayName": "Keyword View"}, {"id": "landing_page_view", "displayName": "Landing Page View"}, {"id": "location_view", "displayName": "Location View"}, {"id": "managed_placement_view", "displayName": "Managed Placement View"}, {"id": "paid_organic_search_term_view", "displayName": "Paid Organic Search Term View"}, {"id": "parental_status_view", "displayName": "Parental Status View"}, {"id": "product_group_view", "displayName": "Product Group View"}, {"id": "search_term_view", "displayName": "Search Term View"}, {"id": "shopping_performance_view", "displayName": "Shopping Performance View"}, {"id": "smart_campaign_search_term_view", "displayName": "Smart Campaign Search Term View"}, {"id": "topic_view", "displayName": "Topic View"}, {"id": "user_location_view", "displayName": "User Location View"}, {"id": "video", "displayName": "Video"}, {"id": "webpage_view", "displayName": "Webpage View"}], "details": {"ad_group": {"fields": ["ad_rotation_mode", "audience_setting.use_audience_grouped", "base_ad_group", "campaign", "cpc_bid_micros", "cpm_bid_micros", "cpv_bid_micros", "display_custom_bid_dimension", "effective_cpc_bid_micros", "effective_target_cpa_micros", "effective_target_cpa_source", "effective_target_roas", "effective_target_roas_source", "excluded_parent_asset_field_types", "explorer_auto_optimizer_setting.opt_in", "final_url_suffix", "id", "labels", "name", "percent_cpc_bid_micros", "resource_name", "status", "target_cpa_micros", "target_cpm_micros", "target_roas", "targeting_setting.target_restrictions", "tracking_url_template", "type", "url_custom_parameters"], "segments": ["ad_destination_type", "ad_network_type", "asset_interaction_target.asset", "asset_interaction_target.interaction_on_this_asset", "auction_insight_domain", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_adjustment", "conversion_attribution_event_type", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "date", "day_of_week", "device", "external_conversion_source", "geo_target_airport", "geo_target_canton", "geo_target_city", "geo_target_country", "geo_target_county", "geo_target_district", "geo_target_metro", "geo_target_most_specific_location", "geo_target_postal_code", "geo_target_province", "geo_target_region", "geo_target_state", "hotel_booking_window_days", "hotel_center_id", "hotel_check_in_date", "hotel_check_in_day_of_week", "hotel_city", "hotel_class", "hotel_country", "hotel_date_selection_type", "hotel_length_of_stay", "hotel_price_bucket", "hotel_rate_rule_id", "hotel_rate_type", "hotel_state", "hour", "interaction_on_this_extension", "month", "month_of_year", "partner_hotel_id", "placeholder_type", "product_aggregator_id", "product_bidding_category_level1", "product_bidding_category_level2", "product_bidding_category_level3", "product_bidding_category_level4", "product_bidding_category_level5", "product_brand", "product_channel", "product_channel_exclusivity", "product_condition", "product_country", "product_custom_attribute0", "product_custom_attribute1", "product_custom_attribute2", "product_custom_attribute3", "product_custom_attribute4", "product_item_id", "product_language", "product_merchant_id", "product_store_id", "product_title", "product_type_l1", "product_type_l2", "product_type_l3", "product_type_l4", "product_type_l5", "quarter", "search_engine_results_page_type", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "auction_insight_search_absolute_top_impression_percentage", "auction_insight_search_impression_share", "auction_insight_search_outranking_share", "auction_insight_search_overlap_rate", "auction_insight_search_position_above_rate", "auction_insight_search_top_impression_percentage", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "average_page_views", "average_time_on_site", "benchmark_average_max_cpc", "biddable_app_install_conversions", "biddable_app_post_install_conversions", "bounce_rate", "clicks", "content_budget_lost_impression_share", "content_impression_share", "content_rank_lost_impression_share", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_by_conversion_date", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cost_per_current_model_attributed_conversion", "cross_device_conversions", "ctr", "current_model_attributed_conversions", "current_model_attributed_conversions_from_interactions_rate", "current_model_attributed_conversions_from_interactions_value_per_interaction", "current_model_attributed_conversions_value", "current_model_attributed_conversions_value_per_cost", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "historical_creative_quality_score", "historical_landing_page_quality_score", "historical_quality_score", "historical_search_predicted_ctr", "hotel_average_lead_value_micros", "hotel_eligible_impressions", "hotel_price_difference_percentage", "impressions", "interaction_event_types", "interaction_rate", "interactions", "message_chat_rate", "message_chats", "message_impressions", "mobile_friendly_clicks_percentage", "percent_new_visitors", "phone_calls", "phone_impressions", "phone_through_rate", "relative_ctr", "search_absolute_top_impression_share", "search_budget_lost_absolute_top_impression_share", "search_budget_lost_impression_share", "search_budget_lost_top_impression_share", "search_click_share", "search_exact_match_impression_share", "search_impression_share", "search_rank_lost_absolute_top_impression_share", "search_rank_lost_impression_share", "search_rank_lost_top_impression_share", "search_top_impression_share", "speed_score", "top_impression_percentage", "valid_accelerated_mobile_pages_clicks_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "value_per_current_model_attributed_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "ad_group_ad": {"fields": ["action_items", "ad.added_by_google_ads", "ad.app_ad.descriptions", "ad.app_ad.headlines", "ad.app_ad.html5_media_bundles", "ad.app_ad.html5_media_bundles.asset", "ad.app_ad.images", "ad.app_ad.images.asset", "ad.app_ad.mandatory_ad_text", "ad.app_ad.youtube_videos", "ad.app_ad.youtube_videos.asset", "ad.app_engagement_ad.descriptions", "ad.app_engagement_ad.headlines", "ad.app_engagement_ad.images", "ad.app_engagement_ad.images.asset", "ad.app_engagement_ad.videos", "ad.app_engagement_ad.videos.asset", "ad.app_pre_registration_ad.descriptions", "ad.app_pre_registration_ad.headlines", "ad.app_pre_registration_ad.images", "ad.app_pre_registration_ad.images.asset", "ad.app_pre_registration_ad.youtube_videos", "ad.app_pre_registration_ad.youtube_videos.asset", "ad.call_ad.business_name", "ad.call_ad.call_tracked", "ad.call_ad.conversion_action", "ad.call_ad.conversion_reporting_state", "ad.call_ad.country_code", "ad.call_ad.description1", "ad.call_ad.description2", "ad.call_ad.disable_call_conversion", "ad.call_ad.headline1", "ad.call_ad.headline2", "ad.call_ad.path1", "ad.call_ad.path2", "ad.call_ad.phone_number", "ad.call_ad.phone_number_verification_url", "ad.device_preference", "ad.discovery_carousel_ad.business_name", "ad.discovery_carousel_ad.call_to_action_text", "ad.discovery_carousel_ad.carousel_cards", "ad.discovery_carousel_ad.carousel_cards.asset", "ad.discovery_carousel_ad.description", "ad.discovery_carousel_ad.headline", "ad.discovery_carousel_ad.logo_image", "ad.discovery_carousel_ad.logo_image.asset", "ad.discovery_multi_asset_ad.business_name", "ad.discovery_multi_asset_ad.call_to_action_text", "ad.discovery_multi_asset_ad.descriptions", "ad.discovery_multi_asset_ad.headlines", "ad.discovery_multi_asset_ad.lead_form_only", "ad.discovery_multi_asset_ad.logo_images", "ad.discovery_multi_asset_ad.logo_images.asset", "ad.discovery_multi_asset_ad.marketing_images", "ad.discovery_multi_asset_ad.marketing_images.asset", "ad.discovery_multi_asset_ad.portrait_marketing_images", "ad.discovery_multi_asset_ad.portrait_marketing_images.asset", "ad.discovery_multi_asset_ad.square_marketing_images", "ad.discovery_multi_asset_ad.square_marketing_images.asset", "ad.display_upload_ad.display_upload_product_type", "ad.display_upload_ad.media_bundle", "ad.display_upload_ad.media_bundle.asset", "ad.display_url", "ad.expanded_dynamic_search_ad.description", "ad.expanded_dynamic_search_ad.description2", "ad.expanded_text_ad.description", "ad.expanded_text_ad.description2", "ad.expanded_text_ad.headline_part1", "ad.expanded_text_ad.headline_part2", "ad.expanded_text_ad.headline_part3", "ad.expanded_text_ad.path1", "ad.expanded_text_ad.path2", "ad.final_app_urls", "ad.final_mobile_urls", "ad.final_url_suffix", "ad.final_urls", "ad.gmail_ad.header_image", "ad.gmail_ad.marketing_image", "ad.gmail_ad.marketing_image_description", "ad.gmail_ad.marketing_image_display_call_to_action.text", "ad.gmail_ad.marketing_image_display_call_to_action.text_color", "ad.gmail_ad.marketing_image_display_call_to_action.url_collection_id", "ad.gmail_ad.marketing_image_headline", "ad.gmail_ad.product_images", "ad.gmail_ad.product_images.product_image", "ad.gmail_ad.product_videos", "ad.gmail_ad.product_videos.product_video", "ad.gmail_ad.teaser.business_name", "ad.gmail_ad.teaser.description", "ad.gmail_ad.teaser.headline", "ad.gmail_ad.teaser.logo_image", "ad.hotel_ad", "ad.id", "ad.image_ad.image_url", "ad.image_ad.media_file", "ad.image_ad.mime_type", "ad.image_ad.name", "ad.image_ad.pixel_height", "ad.image_ad.pixel_width", "ad.image_ad.preview_image_url", "ad.image_ad.preview_pixel_height", "ad.image_ad.preview_pixel_width", "ad.legacy_app_install_ad", "ad.legacy_responsive_display_ad.accent_color", "ad.legacy_responsive_display_ad.allow_flexible_color", "ad.legacy_responsive_display_ad.business_name", "ad.legacy_responsive_display_ad.call_to_action_text", "ad.legacy_responsive_display_ad.description", "ad.legacy_responsive_display_ad.format_setting", "ad.legacy_responsive_display_ad.logo_image", "ad.legacy_responsive_display_ad.long_headline", "ad.legacy_responsive_display_ad.main_color", "ad.legacy_responsive_display_ad.marketing_image", "ad.legacy_responsive_display_ad.price_prefix", "ad.legacy_responsive_display_ad.promo_text", "ad.legacy_responsive_display_ad.short_headline", "ad.legacy_responsive_display_ad.square_logo_image", "ad.legacy_responsive_display_ad.square_marketing_image", "ad.local_ad.call_to_actions", "ad.local_ad.descriptions", "ad.local_ad.headlines", "ad.local_ad.logo_images", "ad.local_ad.logo_images.asset", "ad.local_ad.marketing_images", "ad.local_ad.marketing_images.asset", "ad.local_ad.path1", "ad.local_ad.path2", "ad.local_ad.videos", "ad.local_ad.videos.asset", "ad.name", "ad.resource_name", "ad.responsive_display_ad.accent_color", "ad.responsive_display_ad.allow_flexible_color", "ad.responsive_display_ad.business_name", "ad.responsive_display_ad.call_to_action_text", "ad.responsive_display_ad.control_spec.enable_asset_enhancements", "ad.responsive_display_ad.control_spec.enable_autogen_video", "ad.responsive_display_ad.descriptions", "ad.responsive_display_ad.format_setting", "ad.responsive_display_ad.headlines", "ad.responsive_display_ad.logo_images", "ad.responsive_display_ad.logo_images.asset", "ad.responsive_display_ad.long_headline", "ad.responsive_display_ad.main_color", "ad.responsive_display_ad.marketing_images", "ad.responsive_display_ad.marketing_images.asset", "ad.responsive_display_ad.price_prefix", "ad.responsive_display_ad.promo_text", "ad.responsive_display_ad.square_logo_images", "ad.responsive_display_ad.square_logo_images.asset", "ad.responsive_display_ad.square_marketing_images", "ad.responsive_display_ad.square_marketing_images.asset", "ad.responsive_display_ad.youtube_videos", "ad.responsive_display_ad.youtube_videos.asset", "ad.responsive_search_ad.descriptions", "ad.responsive_search_ad.headlines", "ad.responsive_search_ad.path1", "ad.responsive_search_ad.path2", "ad.shopping_comparison_listing_ad.headline", "ad.shopping_product_ad", "ad.shopping_smart_ad", "ad.smart_campaign_ad.descriptions", "ad.smart_campaign_ad.headlines", "ad.system_managed_resource_source", "ad.text_ad.description1", "ad.text_ad.description2", "ad.text_ad.headline", "ad.tracking_url_template", "ad.type", "ad.url_collections", "ad.url_custom_parameters", "ad.video_ad.bumper.companion_banner.asset", "ad.video_ad.in_feed.description1", "ad.video_ad.in_feed.description2", "ad.video_ad.in_feed.headline", "ad.video_ad.in_feed.thumbnail", "ad.video_ad.in_stream.action_button_label", "ad.video_ad.in_stream.action_headline", "ad.video_ad.in_stream.companion_banner.asset", "ad.video_ad.non_skippable.action_button_label", "ad.video_ad.non_skippable.action_headline", "ad.video_ad.non_skippable.companion_banner.asset", "ad.video_ad.out_stream.description", "ad.video_ad.out_stream.headline", "ad.video_ad.video.asset", "ad.video_responsive_ad.breadcrumb1", "ad.video_responsive_ad.breadcrumb2", "ad.video_responsive_ad.call_to_actions", "ad.video_responsive_ad.companion_banners", "ad.video_responsive_ad.companion_banners.asset", "ad.video_responsive_ad.descriptions", "ad.video_responsive_ad.headlines", "ad.video_responsive_ad.long_headlines", "ad.video_responsive_ad.videos", "ad.video_responsive_ad.videos.asset", "ad_group", "ad_strength", "labels", "policy_summary.approval_status", "policy_summary.policy_topic_entries", "policy_summary.review_status", "resource_name", "status"], "segments": ["ad_destination_type", "ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_adjustment", "conversion_attribution_event_type", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "date", "day_of_week", "device", "external_conversion_source", "interaction_on_this_extension", "keyword.ad_group_criterion", "keyword.info.match_type", "keyword.info.text", "month", "month_of_year", "placeholder_type", "quarter", "search_term_match_type", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "average_page_views", "average_time_on_site", "bounce_rate", "clicks", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_by_conversion_date", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cost_per_current_model_attributed_conversion", "cross_device_conversions", "ctr", "current_model_attributed_conversions", "current_model_attributed_conversions_from_interactions_rate", "current_model_attributed_conversions_from_interactions_value_per_interaction", "current_model_attributed_conversions_value", "current_model_attributed_conversions_value_per_cost", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "percent_new_visitors", "phone_calls", "phone_impressions", "phone_through_rate", "top_impression_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "value_per_current_model_attributed_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "ad_group_ad_asset_combination_view": {"fields": ["enabled", "resource_name", "served_assets"], "segments": ["ad_network_type", "date", "day_of_week", "month", "quarter", "slot", "week", "year"], "metrics": ["impressions"]}, "ad_group_ad_asset_view": {"fields": ["ad_group_ad", "asset", "enabled", "field_type", "performance_label", "policy_summary", "resource_name"], "segments": ["ad_network_type", "date", "day_of_week", "month", "quarter", "slot", "week", "year"], "metrics": ["all_conversions", "all_conversions_value", "all_conversions_value_per_cost", "average_cpc", "biddable_app_install_conversions", "biddable_app_post_install_conversions", "clicks", "conversions", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "ctr", "impressions", "value_per_all_conversions", "value_per_conversion", "view_through_conversions"]}, "ad_group_asset": {"fields": ["ad_group", "asset", "field_type", "resource_name", "source", "status"], "segments": ["ad_network_type", "asset_interaction_target.asset", "asset_interaction_target.interaction_on_this_asset", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "phone_calls", "phone_impressions", "phone_through_rate", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "ad_group_audience_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "hotel_date_selection_type", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_value", "conversions_value_by_conversion_date", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "ad_schedule_view": {"fields": ["resource_name"], "segments": ["conversion_action", "conversion_action_category", "conversion_action_name", "date", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "age_range_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "asset_field_type_view": {"fields": ["field_type", "resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "phone_calls", "phone_impressions", "phone_through_rate", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "asset_group_product_group_view": {"fields": ["asset_group", "asset_group_listing_group_filter", "resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "average_cpm", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "value_per_all_conversions", "value_per_conversion", "view_through_conversions"]}, "bidding_strategy": {"fields": ["campaign_count", "currency_code", "effective_currency_code", "enhanced_cpc", "id", "maximize_conversion_value.cpc_bid_ceiling_micros", "maximize_conversion_value.cpc_bid_floor_micros", "maximize_conversion_value.target_roas", "maximize_conversions.cpc_bid_ceiling_micros", "maximize_conversions.cpc_bid_floor_micros", "maximize_conversions.target_cpa_micros", "name", "non_removed_campaign_count", "resource_name", "status", "target_cpa.cpc_bid_ceiling_micros", "target_cpa.cpc_bid_floor_micros", "target_cpa.target_cpa_micros", "target_impression_share.cpc_bid_ceiling_micros", "target_impression_share.location", "target_impression_share.location_fraction_micros", "target_roas.cpc_bid_ceiling_micros", "target_roas.cpc_bid_floor_micros", "target_roas.target_roas", "target_spend.cpc_bid_ceiling_micros", "target_spend.target_spend_micros", "type"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "hour", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "campaign": {"fields": ["accessible_bidding_strategy", "ad_serving_optimization_status", "advertising_channel_sub_type", "advertising_channel_type", "app_campaign_setting.app_id", "app_campaign_setting.app_store", "app_campaign_setting.bidding_strategy_goal_type", "audience_setting.use_audience_grouped", "base_campaign", "bidding_strategy", "bidding_strategy_type", "campaign_budget", "campaign_group", "commission.commission_rate_micros", "dynamic_search_ads_setting.domain_name", "dynamic_search_ads_setting.feeds", "dynamic_search_ads_setting.language_code", "dynamic_search_ads_setting.use_supplied_urls_only", "end_date", "excluded_parent_asset_field_types", "experiment_type", "final_url_suffix", "frequency_caps", "geo_target_type_setting.negative_geo_target_type", "geo_target_type_setting.positive_geo_target_type", "hotel_setting.hotel_center_id", "id", "labels", "local_campaign_setting.location_source_type", "local_services_campaign_settings.category_bids", "manual_cpa", "manual_cpc.enhanced_cpc_enabled", "manual_cpm", "manual_cpv", "maximize_conversion_value.target_roas", "maximize_conversions.target_cpa_micros", "name", "network_settings.target_content_network", "network_settings.target_google_search", "network_settings.target_partner_search_network", "network_settings.target_search_network", "optimization_goal_setting.optimization_goal_types", "optimization_score", "payment_mode", "percent_cpc.cpc_bid_ceiling_micros", "percent_cpc.enhanced_cpc_enabled", "performance_max_upgrade.performance_max_campaign", "performance_max_upgrade.pre_upgrade_campaign", "performance_max_upgrade.status", "real_time_bidding_setting.opt_in", "resource_name", "selective_optimization.conversion_actions", "serving_status", "shopping_setting.campaign_priority", "shopping_setting.enable_local", "shopping_setting.feed_label", "shopping_setting.merchant_id", "shopping_setting.sales_country", "shopping_setting.use_vehicle_inventory", "start_date", "status", "target_cpa.cpc_bid_ceiling_micros", "target_cpa.cpc_bid_floor_micros", "target_cpa.target_cpa_micros", "target_cpm", "target_impression_share.cpc_bid_ceiling_micros", "target_impression_share.location", "target_impression_share.location_fraction_micros", "target_roas.cpc_bid_ceiling_micros", "target_roas.cpc_bid_floor_micros", "target_roas.target_roas", "target_spend.cpc_bid_ceiling_micros", "target_spend.target_spend_micros", "targeting_setting.target_restrictions", "tracking_setting.tracking_url", "tracking_url_template", "url_custom_parameters", "url_expansion_opt_out", "vanity_pharma.vanity_pharma_display_url_mode", "vanity_pharma.vanity_pharma_text", "video_brand_safety_suitability"], "segments": ["ad_destination_type", "ad_network_type", "asset_interaction_target.asset", "asset_interaction_target.interaction_on_this_asset", "auction_insight_domain", "budget_campaign_association_status.campaign", "budget_campaign_association_status.status", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_adjustment", "conversion_attribution_event_type", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "conversion_value_rule_primary_dimension", "date", "day_of_week", "device", "external_conversion_source", "geo_target_airport", "geo_target_canton", "geo_target_city", "geo_target_country", "geo_target_county", "geo_target_district", "geo_target_metro", "geo_target_most_specific_location", "geo_target_postal_code", "geo_target_province", "geo_target_region", "geo_target_state", "hotel_booking_window_days", "hotel_center_id", "hotel_check_in_date", "hotel_check_in_day_of_week", "hotel_city", "hotel_class", "hotel_country", "hotel_date_selection_type", "hotel_length_of_stay", "hotel_price_bucket", "hotel_rate_rule_id", "hotel_rate_type", "hotel_state", "hour", "interaction_on_this_extension", "month", "month_of_year", "partner_hotel_id", "placeholder_type", "product_aggregator_id", "product_bidding_category_level1", "product_bidding_category_level2", "product_bidding_category_level3", "product_bidding_category_level4", "product_bidding_category_level5", "product_brand", "product_channel", "product_channel_exclusivity", "product_condition", "product_country", "product_custom_attribute0", "product_custom_attribute1", "product_custom_attribute2", "product_custom_attribute3", "product_custom_attribute4", "product_item_id", "product_language", "product_merchant_id", "product_store_id", "product_title", "product_type_l1", "product_type_l2", "product_type_l3", "product_type_l4", "product_type_l5", "quarter", "recommendation_type", "search_engine_results_page_type", "sk_ad_network_ad_event_type", "sk_ad_network_attribution_credit", "sk_ad_network_conversion_value", "sk_ad_network_source_app.sk_ad_network_source_app_id", "sk_ad_network_user_type", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_click_to_call", "all_conversions_from_directions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_from_menu", "all_conversions_from_order", "all_conversions_from_other_engagement", "all_conversions_from_store_visit", "all_conversions_from_store_website", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "auction_insight_search_absolute_top_impression_percentage", "auction_insight_search_impression_share", "auction_insight_search_outranking_share", "auction_insight_search_overlap_rate", "auction_insight_search_position_above_rate", "auction_insight_search_top_impression_percentage", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "average_page_views", "average_time_on_site", "benchmark_average_max_cpc", "bounce_rate", "clicks", "content_budget_lost_impression_share", "content_impression_share", "content_rank_lost_impression_share", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_by_conversion_date", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cost_per_current_model_attributed_conversion", "cross_device_conversions", "ctr", "current_model_attributed_conversions", "current_model_attributed_conversions_from_interactions_rate", "current_model_attributed_conversions_from_interactions_value_per_interaction", "current_model_attributed_conversions_value", "current_model_attributed_conversions_value_per_cost", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "historical_creative_quality_score", "historical_landing_page_quality_score", "historical_quality_score", "historical_search_predicted_ctr", "hotel_average_lead_value_micros", "hotel_eligible_impressions", "hotel_price_difference_percentage", "impressions", "interaction_event_types", "interaction_rate", "interactions", "invalid_click_rate", "invalid_clicks", "message_chat_rate", "message_chats", "message_impressions", "mobile_friendly_clicks_percentage", "optimization_score_uplift", "optimization_score_url", "percent_new_visitors", "phone_calls", "phone_impressions", "phone_through_rate", "relative_ctr", "search_absolute_top_impression_share", "search_budget_lost_absolute_top_impression_share", "search_budget_lost_impression_share", "search_budget_lost_top_impression_share", "search_click_share", "search_exact_match_impression_share", "search_impression_share", "search_rank_lost_absolute_top_impression_share", "search_rank_lost_impression_share", "search_rank_lost_top_impression_share", "search_top_impression_share", "sk_ad_network_conversions", "speed_score", "top_impression_percentage", "valid_accelerated_mobile_pages_clicks_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "value_per_current_model_attributed_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "campaign_asset": {"fields": ["asset", "campaign", "field_type", "resource_name", "source", "status"], "segments": ["ad_network_type", "asset_interaction_target.asset", "asset_interaction_target.interaction_on_this_asset", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "phone_calls", "phone_impressions", "phone_through_rate", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "campaign_audience_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "hotel_date_selection_type", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_value", "conversions_value_by_conversion_date", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "campaign_bid_modifier": {"fields": ["bid_modifier", "campaign", "criterion_id", "interaction_type.type", "resource_name"], "segments": [], "metrics": ["interaction_event_types"]}, "campaign_budget": {"fields": ["amount_micros", "delivery_method", "explicitly_shared", "has_recommended_budget", "id", "name", "period", "recommended_budget_amount_micros", "recommended_budget_estimated_change_weekly_clicks", "recommended_budget_estimated_change_weekly_cost_micros", "recommended_budget_estimated_change_weekly_interactions", "recommended_budget_estimated_change_weekly_views", "reference_count", "resource_name", "status", "total_amount_micros", "type"], "segments": ["budget_campaign_association_status.campaign", "budget_campaign_association_status.status", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "external_conversion_source"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "campaign_group": {"fields": ["id", "name", "resource_name", "status"], "segments": ["ad_destination_type", "date", "device", "hour", "month", "quarter", "week", "year"], "metrics": ["average_cost", "average_cpv", "conversions", "cost_per_conversion", "interaction_rate", "interactions"]}, "click_view": {"fields": ["ad_group_ad", "area_of_interest.city", "area_of_interest.country", "area_of_interest.metro", "area_of_interest.most_specific", "area_of_interest.region", "campaign_location_target", "gclid", "keyword", "keyword_info.match_type", "keyword_info.text", "location_of_presence.city", "location_of_presence.country", "location_of_presence.metro", "location_of_presence.most_specific", "location_of_presence.region", "page_number", "resource_name", "user_list"], "segments": ["ad_network_type", "click_type", "date", "device", "month_of_year", "slot"], "metrics": ["clicks"]}, "conversion_action": {"fields": ["app_id", "attribution_model_settings.attribution_model", "attribution_model_settings.data_driven_model_status", "category", "click_through_lookback_window_days", "counting_type", "firebase_settings.event_name", "firebase_settings.project_id", "firebase_settings.property_id", "firebase_settings.property_name", "id", "include_in_conversions_metric", "mobile_app_vendor", "name", "origin", "owner_customer", "phone_call_duration_seconds", "primary_for_goal", "resource_name", "status", "tag_snippets", "third_party_app_analytics_settings.event_name", "third_party_app_analytics_settings.provider_name", "type", "value_settings.always_use_default_value", "value_settings.default_currency_code", "value_settings.default_value", "view_through_lookback_window_days"], "segments": ["date", "month", "quarter", "week"], "metrics": ["all_conversions", "all_conversions_value", "conversion_last_conversion_date", "conversion_last_received_request_date_time"]}, "customer": {"fields": ["auto_tagging_enabled", "call_reporting_setting.call_conversion_action", "call_reporting_setting.call_conversion_reporting_enabled", "call_reporting_setting.call_reporting_enabled", "conversion_tracking_setting.accepted_customer_data_terms", "conversion_tracking_setting.conversion_tracking_id", "conversion_tracking_setting.conversion_tracking_status", "conversion_tracking_setting.cross_account_conversion_tracking_id", "conversion_tracking_setting.enhanced_conversions_for_leads_enabled", "conversion_tracking_setting.google_ads_conversion_customer", "currency_code", "descriptive_name", "final_url_suffix", "has_partners_badge", "id", "manager", "optimization_score", "optimization_score_weight", "pay_per_conversion_eligibility_failure_reasons", "remarketing_setting.google_global_site_tag", "resource_name", "status", "test_account", "time_zone", "tracking_url_template"], "segments": ["ad_network_type", "auction_insight_domain", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_adjustment", "conversion_attribution_event_type", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "conversion_value_rule_primary_dimension", "date", "day_of_week", "device", "external_conversion_source", "geo_target_airport", "geo_target_city", "geo_target_metro", "geo_target_region", "hour", "month", "month_of_year", "product_aggregator_id", "product_bidding_category_level1", "product_bidding_category_level2", "product_bidding_category_level3", "product_bidding_category_level4", "product_bidding_category_level5", "product_brand", "product_channel", "product_channel_exclusivity", "product_condition", "product_country", "product_custom_attribute0", "product_custom_attribute1", "product_custom_attribute2", "product_custom_attribute3", "product_custom_attribute4", "product_item_id", "product_language", "product_merchant_id", "product_store_id", "product_title", "product_type_l1", "product_type_l2", "product_type_l3", "product_type_l4", "product_type_l5", "quarter", "recommendation_type", "search_engine_results_page_type", "sk_ad_network_ad_event_type", "sk_ad_network_attribution_credit", "sk_ad_network_conversion_value", "sk_ad_network_source_app.sk_ad_network_source_app_id", "sk_ad_network_user_type", "slot", "week", "year"], "metrics": []}, "customer_asset": {"fields": ["asset", "field_type", "resource_name", "source", "status"], "segments": ["ad_network_type", "asset_interaction_target.asset", "asset_interaction_target.interaction_on_this_asset", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "phone_calls", "phone_impressions", "phone_through_rate", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "detail_placement_view": {"fields": ["display_name", "group_placement_target_url", "placement", "placement_type", "resource_name", "target_url"], "segments": ["ad_network_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "display_keyword_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "distance_view": {"fields": ["distance_bucket", "metric_system", "resource_name"], "segments": ["ad_network_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "average_cpm", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "value_per_all_conversions", "value_per_conversion", "view_through_conversions"]}, "dynamic_search_ads_search_term_view": {"fields": ["has_matching_keyword", "has_negative_keyword", "has_negative_url", "headline", "landing_page", "page_url", "resource_name", "search_term"], "segments": ["conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "external_conversion_source", "month", "month_of_year", "quarter", "webpage", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "average_cpm", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "value_per_all_conversions", "value_per_conversion"]}, "expanded_landing_page_view": {"fields": ["expanded_final_url", "resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "mobile_friendly_clicks_percentage", "speed_score", "valid_accelerated_mobile_pages_clicks_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "extension_feed_item": {"fields": ["ad_schedules", "affiliate_location_feed_item.address_line_1", "affiliate_location_feed_item.address_line_2", "affiliate_location_feed_item.business_name", "affiliate_location_feed_item.chain_id", "affiliate_location_feed_item.chain_name", "affiliate_location_feed_item.city", "affiliate_location_feed_item.country_code", "affiliate_location_feed_item.phone_number", "affiliate_location_feed_item.postal_code", "affiliate_location_feed_item.province", "app_feed_item.app_id", "app_feed_item.app_store", "app_feed_item.final_mobile_urls", "app_feed_item.final_url_suffix", "app_feed_item.final_urls", "app_feed_item.link_text", "app_feed_item.tracking_url_template", "call_feed_item.call_conversion_action", "call_feed_item.call_conversion_reporting_state", "call_feed_item.call_conversion_tracking_disabled", "call_feed_item.call_tracking_enabled", "call_feed_item.country_code", "call_feed_item.phone_number", "callout_feed_item.callout_text", "device", "end_date_time", "extension_type", "hotel_callout_feed_item.language_code", "hotel_callout_feed_item.text", "id", "image_feed_item.image_asset", "location_feed_item.address_line_1", "location_feed_item.address_line_2", "location_feed_item.business_name", "location_feed_item.city", "location_feed_item.country_code", "location_feed_item.phone_number", "location_feed_item.postal_code", "location_feed_item.province", "price_feed_item.final_url_suffix", "price_feed_item.language_code", "price_feed_item.price_offerings", "price_feed_item.price_qualifier", "price_feed_item.tracking_url_template", "price_feed_item.type", "promotion_feed_item.discount_modifier", "promotion_feed_item.final_mobile_urls", "promotion_feed_item.final_url_suffix", "promotion_feed_item.final_urls", "promotion_feed_item.language_code", "promotion_feed_item.money_amount_off.amount_micros", "promotion_feed_item.money_amount_off.currency_code", "promotion_feed_item.occasion", "promotion_feed_item.orders_over_amount.amount_micros", "promotion_feed_item.orders_over_amount.currency_code", "promotion_feed_item.percent_off", "promotion_feed_item.promotion_code", "promotion_feed_item.promotion_end_date", "promotion_feed_item.promotion_start_date", "promotion_feed_item.promotion_target", "promotion_feed_item.tracking_url_template", "resource_name", "sitelink_feed_item.final_mobile_urls", "sitelink_feed_item.final_url_suffix", "sitelink_feed_item.final_urls", "sitelink_feed_item.line1", "sitelink_feed_item.line2", "sitelink_feed_item.link_text", "sitelink_feed_item.tracking_url_template", "start_date_time", "status", "structured_snippet_feed_item.header", "structured_snippet_feed_item.values", "targeted_ad_group", "targeted_campaign", "targeted_geo_target_constant", "targeted_keyword.match_type", "targeted_keyword.text", "text_message_feed_item.business_name", "text_message_feed_item.country_code", "text_message_feed_item.extension_text", "text_message_feed_item.phone_number", "text_message_feed_item.text"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "interaction_on_this_extension", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": []}, "feed_item": {"fields": ["attribute_values", "end_date_time", "feed", "geo_targeting_restriction", "id", "policy_infos", "resource_name", "start_date_time", "status", "url_custom_parameters"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "interaction_on_this_extension", "month", "month_of_year", "placeholder_type", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "feed_placeholder_view": {"fields": ["placeholder_type", "resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "gender_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "geographic_view": {"fields": ["country_criterion_id", "location_type", "resource_name"], "segments": ["ad_network_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "geo_target_airport", "geo_target_canton", "geo_target_city", "geo_target_county", "geo_target_district", "geo_target_metro", "geo_target_most_specific_location", "geo_target_postal_code", "geo_target_province", "geo_target_region", "geo_target_state", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "group_placement_view": {"fields": ["display_name", "placement", "placement_type", "resource_name", "target_url"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "hotel_group_view": {"fields": ["resource_name"], "segments": ["date", "day_of_week", "hour", "month", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cpc", "average_cpm", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "hotel_average_lead_value_micros", "hotel_eligible_impressions", "impressions", "search_absolute_top_impression_share", "search_budget_lost_absolute_top_impression_share", "search_budget_lost_impression_share", "search_budget_lost_top_impression_share", "search_click_share", "search_impression_share", "search_rank_lost_absolute_top_impression_share", "search_rank_lost_impression_share", "search_rank_lost_top_impression_share", "search_top_impression_share", "value_per_all_conversions", "value_per_conversion"]}, "hotel_reconciliation": {"fields": ["billed", "campaign", "check_in_date", "check_out_date", "commission_id", "hotel_center_id", "hotel_id", "order_id", "reconciled_value_micros", "resource_name", "status"], "segments": ["date"], "metrics": ["hotel_commission_rate_micros", "hotel_expected_commission_cost", "value_per_conversions_by_conversion_date"]}, "income_range_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "keyword_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_adjustment", "conversion_attribution_event_type", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_by_conversion_date", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_by_conversion_date", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "average_page_views", "average_time_on_site", "bounce_rate", "clicks", "conversions", "conversions_by_conversion_date", "conversions_from_interactions_rate", "conversions_value", "conversions_value_by_conversion_date", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cost_per_current_model_attributed_conversion", "cross_device_conversions", "ctr", "current_model_attributed_conversions", "current_model_attributed_conversions_from_interactions_rate", "current_model_attributed_conversions_from_interactions_value_per_interaction", "current_model_attributed_conversions_value", "current_model_attributed_conversions_value_per_cost", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "historical_creative_quality_score", "historical_landing_page_quality_score", "historical_quality_score", "historical_search_predicted_ctr", "impressions", "interaction_event_types", "interaction_rate", "interactions", "percent_new_visitors", "search_absolute_top_impression_share", "search_budget_lost_absolute_top_impression_share", "search_budget_lost_impression_share", "search_budget_lost_top_impression_share", "search_click_share", "search_exact_match_impression_share", "search_impression_share", "search_rank_lost_absolute_top_impression_share", "search_rank_lost_impression_share", "search_rank_lost_top_impression_share", "search_top_impression_share", "top_impression_percentage", "value_per_all_conversions", "value_per_all_conversions_by_conversion_date", "value_per_conversion", "value_per_conversions_by_conversion_date", "value_per_current_model_attributed_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "landing_page_view": {"fields": ["resource_name", "unexpanded_final_url"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "mobile_friendly_clicks_percentage", "speed_score", "valid_accelerated_mobile_pages_clicks_percentage", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views"]}, "location_view": {"fields": ["resource_name"], "segments": ["conversion_action", "conversion_action_category", "conversion_action_name", "date", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "managed_placement_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "paid_organic_search_term_view": {"fields": ["resource_name", "search_term"], "segments": ["date", "day_of_week", "device", "keyword.ad_group_criterion", "keyword.info.match_type", "keyword.info.text", "month", "month_of_year", "quarter", "search_engine_results_page_type", "week", "year"], "metrics": ["average_cpc", "clicks", "combined_clicks", "combined_clicks_per_query", "combined_queries", "ctr", "impressions", "organic_clicks", "organic_clicks_per_query", "organic_impressions", "organic_impressions_per_query", "organic_queries"]}, "parental_status_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "product_group_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "average_cpm", "benchmark_average_max_cpc", "benchmark_ctr", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "search_absolute_top_impression_share", "search_click_share", "search_impression_share", "value_per_all_conversions", "value_per_conversion", "view_through_conversions"]}, "search_term_view": {"fields": ["ad_group", "resource_name", "search_term", "status"], "segments": ["ad_network_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "keyword.ad_group_criterion", "keyword.info.match_type", "keyword.info.text", "month", "month_of_year", "quarter", "search_term_match_type", "week", "year"], "metrics": ["absolute_top_impression_percentage", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "shopping_performance_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "product_aggregator_id", "product_bidding_category_level1", "product_bidding_category_level2", "product_bidding_category_level3", "product_bidding_category_level4", "product_bidding_category_level5", "product_brand", "product_channel", "product_channel_exclusivity", "product_condition", "product_country", "product_custom_attribute0", "product_custom_attribute1", "product_custom_attribute2", "product_custom_attribute3", "product_custom_attribute4", "product_item_id", "product_language", "product_merchant_id", "product_store_id", "product_title", "product_type_l1", "product_type_l2", "product_type_l3", "product_type_l4", "product_type_l5", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "search_absolute_top_impression_share", "search_click_share", "search_impression_share", "value_per_all_conversions", "value_per_conversion"]}, "smart_campaign_search_term_view": {"fields": ["campaign", "resource_name", "search_term"], "segments": ["date", "day_of_week", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["clicks", "cost_micros", "impressions"]}, "topic_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_from_interactions_value_per_interaction", "all_conversions_value", "all_conversions_value_per_cost", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "gmail_forwards", "gmail_saves", "gmail_secondary_clicks", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "user_location_view": {"fields": ["country_criterion_id", "resource_name", "targeting_location"], "segments": ["ad_network_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "geo_target_airport", "geo_target_canton", "geo_target_city", "geo_target_county", "geo_target_district", "geo_target_metro", "geo_target_most_specific_location", "geo_target_postal_code", "geo_target_province", "geo_target_region", "geo_target_state", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "impressions", "interaction_event_types", "interaction_rate", "interactions", "value_per_all_conversions", "value_per_conversion", "video_view_rate", "video_views", "view_through_conversions"]}, "video": {"fields": ["channel_id", "duration_millis", "id", "resource_name", "title"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "week", "year"], "metrics": ["all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_from_interactions_value_per_interaction", "conversions_value", "conversions_value_per_cost", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cross_device_conversions", "ctr", "engagement_rate", "engagements", "impressions", "value_per_all_conversions", "value_per_conversion", "video_quartile_p100_rate", "video_quartile_p25_rate", "video_quartile_p50_rate", "video_quartile_p75_rate", "video_view_rate", "video_views", "view_through_conversions"]}, "webpage_view": {"fields": ["resource_name"], "segments": ["ad_network_type", "click_type", "conversion_action", "conversion_action_category", "conversion_action_name", "conversion_lag_bucket", "conversion_or_adjustment_lag_bucket", "date", "day_of_week", "device", "external_conversion_source", "month", "month_of_year", "quarter", "slot", "week", "year"], "metrics": ["absolute_top_impression_percentage", "active_view_cpm", "active_view_ctr", "active_view_impressions", "active_view_measurability", "active_view_measurable_cost_micros", "active_view_measurable_impressions", "active_view_viewability", "all_conversions", "all_conversions_from_interactions_rate", "all_conversions_value", "average_cost", "average_cpc", "average_cpe", "average_cpm", "average_cpv", "clicks", "conversions", "conversions_from_interactions_rate", "conversions_value", "cost_micros", "cost_per_all_conversions", "cost_per_conversion", "cost_per_current_model_attributed_conversion", "cross_device_conversions", "ctr", "current_model_attributed_conversions", "current_model_attributed_conversions_value", "engagement_rate", "engagements", "impressions", "interaction_event_types", "interaction_rate", "interactions", "top_impression_percentage", "value_per_all_conversions", "value_per_conversion", "value_per_current_model_attributed_conversion", "view_through_conversions"]}}}, "resourceWithoutMetric": {"name": [{"id": "accessible_bidding_strategy", "displayName": "Accessible Bidding Strategy"}, {"id": "account_budget", "displayName": "Account Budget"}, {"id": "account_budget_proposal", "displayName": "Account Budget Proposal"}, {"id": "account_link", "displayName": "Account <PERSON>"}, {"id": "ad_group_ad_label", "displayName": "Ad Group Ad Label"}, {"id": "ad_group_bid_modifier", "displayName": "Ad Group Bid Modifier"}, {"id": "ad_group_criterion", "displayName": "Ad Group Criterion"}, {"id": "ad_group_criterion_customizer", "displayName": "Ad Group Criterion Customizer"}, {"id": "ad_group_criterion_label", "displayName": "Ad Group Criterion Label"}, {"id": "ad_group_criterion_simulation", "displayName": "Ad Group Criterion Simulation"}, {"id": "ad_group_customizer", "displayName": "Ad Group Customizer"}, {"id": "ad_group_extension_setting", "displayName": "Ad Group Extension Setting"}, {"id": "ad_group_feed", "displayName": "Ad Group Feed"}, {"id": "ad_group_label", "displayName": "Ad Group Label"}, {"id": "ad_group_simulation", "displayName": "Ad Group Simulation"}, {"id": "ad_parameter", "displayName": "Ad Parameter"}, {"id": "asset", "displayName": "<PERSON><PERSON>"}, {"id": "asset_group", "displayName": "Asset Group"}, {"id": "asset_group_asset", "displayName": "Asset Group Asset"}, {"id": "asset_group_listing_group_filter", "displayName": "Asset Group Listing Group Filter"}, {"id": "asset_group_signal", "displayName": "Asset Group Signal"}, {"id": "asset_set", "displayName": "Asset Set"}, {"id": "asset_set_asset", "displayName": "Asset Set Asset"}, {"id": "audience", "displayName": "Audience"}, {"id": "batch_job", "displayName": "<PERSON><PERSON>"}, {"id": "bidding_data_exclusion", "displayName": "Binding Data Exclusion"}, {"id": "bidding_seasonality_adjustment", "displayName": "Bidding Seasonality Adjustment"}, {"id": "bidding_strategy_simulation", "displayName": "Bidding Strategy Simulation"}, {"id": "billing_setup", "displayName": "Billing Setup"}, {"id": "call_view", "displayName": "Call View"}, {"id": "campaign_asset_set", "displayName": "Campaign Asset Set"}, {"id": "campaign_conversion_goal", "displayName": "Campaign Conversion Goal"}, {"id": "campaign_criterion", "displayName": "Campaign Criterion"}, {"id": "campaign_criterion_simulation", "displayName": "Campaign Criterion Simulation"}, {"id": "campaign_customizer", "displayName": "Campaign Customizer"}, {"id": "campaign_draft", "displayName": "Campaign Draft"}, {"id": "campaign_experiment", "displayName": "Campaign Experiment"}, {"id": "campaign_extension_setting", "displayName": "Campaign Extension Setting"}, {"id": "campaign_feed", "displayName": "Campaign Feed"}, {"id": "campaign_label", "displayName": "Campaign Label"}, {"id": "campaign_shared_set", "displayName": "Campaign Shared Set"}, {"id": "campaign_simulation", "displayName": "Campaign Simulation"}, {"id": "carrier_constant", "displayName": "Carrier Constant"}, {"id": "change_event", "displayName": "Change Event"}, {"id": "change_status", "displayName": "Change Status"}, {"id": "combined_audience", "displayName": "Combined Audience"}, {"id": "conversion_custom_variable", "displayName": "Conversion Custom Variable"}, {"id": "conversion_goal_campaign_config", "displayName": "Conversion Goal Campaign Config"}, {"id": "conversion_value_rule", "displayName": "Conversion Value Rule"}, {"id": "conversion_value_rule_set", "displayName": "Conversion Value Rule"}, {"id": "currency_constant", "displayName": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "custom_audience", "displayName": "Custom Audience"}, {"id": "custom_conversion_goal", "displayName": "Custom Conversion Goal"}, {"id": "custom_interest", "displayName": "Custom Interest"}, {"id": "customer_client", "displayName": "Customer Client"}, {"id": "customer_client_link", "displayName": "Customer Client Link"}, {"id": "customer_conversion_goal", "displayName": "Customer Conversion Goal"}, {"id": "customer_customizer", "displayName": "Customer Customizer"}, {"id": "customer_extension_setting", "displayName": "Customer Extension Setting"}, {"id": "customer_feed", "displayName": "Customer Feed"}, {"id": "customer_label", "displayName": "Customer Label"}, {"id": "customer_manager_link", "displayName": "Customer Managed Link"}, {"id": "customer_negative_criterion", "displayName": "Customer Negative Criterion"}, {"id": "customer_user_access", "displayName": "Customer User Access"}, {"id": "customer_user_access_invitation", "displayName": "Customer User Access Invitation"}, {"id": "customizer_attribute", "displayName": "Customizer Attribute"}, {"id": "detailed_demographic", "displayName": "Detailed Demographic"}, {"id": "domain_category", "displayName": "Domain Category"}, {"id": "experiment", "displayName": "Experiment"}, {"id": "experiment_arm", "displayName": "Experiment Arm"}, {"id": "feed", "displayName": "Feed"}, {"id": "feed_item_set", "displayName": "Feed Item <PERSON>"}, {"id": "feed_item_set_link", "displayName": "Feed Item <PERSON>"}, {"id": "feed_item_target", "displayName": "Feed Item Target"}, {"id": "feed_mapping", "displayName": "Feed Mapping"}, {"id": "geo_target_constant", "displayName": "Geo Target Constant "}, {"id": "keyword_plan", "displayName": "Keyword Plan"}, {"id": "keyword_plan_ad_group", "displayName": "Keyword Plan Ad Group"}, {"id": "keyword_plan_ad_group_keyword", "displayName": "Keyword Plan Ad Group Keyword"}, {"id": "keyword_plan_campaign", "displayName": "Keyword Plan Campaign"}, {"id": "keyword_plan_campaign_keyword", "displayName": "Keyword Plan Campaign Keyword"}, {"id": "label", "displayName": "Label"}, {"id": "language_constant", "displayName": "Language Constant"}, {"id": "lead_form_submission_data", "displayName": "Lead Form Submission Data"}, {"id": "life_event", "displayName": "Life Event"}, {"id": "media_file", "displayName": "Media File"}, {"id": "mobile_app_category_constant", "displayName": "Mobile App Category Constant"}, {"id": "mobile_device_constant", "displayName": "Mobile Device Constant"}, {"id": "offline_user_data_job", "displayName": "Offline User Data Job"}, {"id": "operating_system_version_constant", "displayName": "Operating System Version Constant"}, {"id": "product_bidding_category_constant", "displayName": "Product Bidding Category Constant"}, {"id": "recommendation", "displayName": "Recommendation"}, {"id": "remarketing_action", "displayName": "Remarketing Action"}, {"id": "shared_criterion", "displayName": "Shared Criterion"}, {"id": "shared_set", "displayName": "Shared Set"}, {"id": "smart_campaign_setting", "displayName": "Smart Campaign Setting"}, {"id": "third_party_app_analytics_link", "displayName": "Third Party App Analytics Link"}, {"id": "topic_constant", "displayName": "Topic Constant"}, {"id": "user_interest", "displayName": "User Interest"}, {"id": "user_list", "displayName": "User List"}]}}}