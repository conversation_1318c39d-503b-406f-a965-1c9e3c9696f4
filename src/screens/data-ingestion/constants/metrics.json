[{"expression": "ga:users", "alias": "ga_users", "dataType": "int64"}, {"expression": "ga:visitors", "alias": "ga_visitors", "dataType": "int64"}, {"expression": "ga:newUsers", "alias": "ga_newUsers", "dataType": "int64"}, {"expression": "ga:newVisits", "alias": "ga_newVisits", "dataType": "int64"}, {"expression": "ga:percentNewSessions", "alias": "ga_percentNewSessions", "dataType": "float"}, {"expression": "ga:percentNewVisits", "alias": "ga_percentNewVisits", "dataType": "float"}, {"expression": "ga:1dayUsers", "alias": "ga_1dayUsers", "dataType": "int64"}, {"expression": "ga:7dayUsers", "alias": "ga_7dayUsers", "dataType": "int64"}, {"expression": "ga:14dayUsers", "alias": "ga_14dayUsers", "dataType": "int64"}, {"expression": "ga:28dayUsers", "alias": "ga_28dayUsers", "dataType": "int64"}, {"expression": "ga:30dayUsers", "alias": "ga_30dayUsers", "dataType": "int64"}, {"expression": "ga:sessions", "alias": "ga_sessions", "dataType": "int64"}, {"expression": "ga:visits", "alias": "ga_visits", "dataType": "int64"}, {"expression": "ga:bounces", "alias": "ga_bounces", "dataType": "int64"}, {"expression": "ga:bounceRate", "alias": "ga_bounceRate", "dataType": "float"}, {"expression": "ga:visitBounceRate", "alias": "ga_visitBounceRate", "dataType": "float"}, {"expression": "ga:sessionDuration", "alias": "ga_sessionDuration", "dataType": "float"}, {"expression": "ga:avgSessionDuration", "alias": "ga_avgSessionDuration", "dataType": "float"}, {"expression": "ga:organicSearches", "alias": "ga_organicSearches", "dataType": "int64"}, {"expression": "ga:impressions", "alias": "ga_impressions", "dataType": "int64"}, {"expression": "ga:adClicks", "alias": "ga_adClicks", "dataType": "int64"}, {"expression": "ga:adCost", "alias": "ga_adCost", "dataType": "float"}, {"expression": "ga:CPM", "alias": "ga_CPM", "dataType": "float"}, {"expression": "ga:CPC", "alias": "ga_CPC", "dataType": "float"}, {"expression": "ga:CTR", "alias": "ga_CTR", "dataType": "float"}, {"expression": "ga:costPerTransaction", "alias": "ga_costPerTransaction", "dataType": "float"}, {"expression": "ga:costPerGoalConversion", "alias": "ga_costPerGoalConversion", "dataType": "float"}, {"expression": "ga:costPerConversion", "alias": "ga_costPerConversion", "dataType": "float"}, {"expression": "ga:RPC", "alias": "ga_RPC", "dataType": "float"}, {"expression": "ga:ROI", "alias": "ga_ROI", "dataType": "float"}, {"expression": "ga:margin", "alias": "ga_margin", "dataType": "float"}, {"expression": "ga:ROAS", "alias": "ga_ROAS", "dataType": "float"}, {"expression": "ga:goalXXStarts", "alias": "ga_goalXXStarts", "dataType": "int64"}, {"expression": "ga:goalStartsAll", "alias": "ga_goalStartsAll", "dataType": "int64"}, {"expression": "ga:goalXXCompletions", "alias": "ga_goalXXCompletions", "dataType": "int64"}, {"expression": "ga:goalCompletionsAll", "alias": "ga_goalCompletionsAll", "dataType": "int64"}, {"expression": "ga:goalXXValue", "alias": "ga_goalXXValue", "dataType": "float"}, {"expression": "ga:goalValueAll", "alias": "ga_goalValueAll", "dataType": "float"}, {"expression": "ga:goalValuePerSession", "alias": "ga_goalValuePerSession", "dataType": "float"}, {"expression": "ga:goalValuePerVisit", "alias": "ga_goalValuePerVisit", "dataType": "float"}, {"expression": "ga:goalXXConversionRate", "alias": "ga_goalXXConversionRate", "dataType": "float"}, {"expression": "ga:goalConversionRateAll", "alias": "ga_goalConversionRateAll", "dataType": "float"}, {"expression": "ga:goalXXAbandons", "alias": "ga_goalXXAbandons", "dataType": "int64"}, {"expression": "ga:goalAbandonsAll", "alias": "ga_goalAbandonsAll", "dataType": "int64"}, {"expression": "ga:goalXXAbandonRate", "alias": "ga_goalXXAbandonRate", "dataType": "float"}, {"expression": "ga:goalAbandonRateAll", "alias": "ga_goalAbandonRateAll", "dataType": "float"}, {"expression": "ga:pageValue", "alias": "ga_pageValue", "dataType": "float"}, {"expression": "ga:entrances", "alias": "ga_entrances", "dataType": "int64"}, {"expression": "ga:entranceRate", "alias": "ga_entranceRate", "dataType": "float"}, {"expression": "ga:pageviews", "alias": "ga_pageviews", "dataType": "int64"}, {"expression": "ga:pageviewsPerSession", "alias": "ga_pageviewsPerSession", "dataType": "float"}, {"expression": "ga:pageviewsPerVisit", "alias": "ga_pageviewsPerVisit", "dataType": "float"}, {"expression": "ga:contentGroupUniqueViewsXX", "alias": "ga_contentGroupUniqueViewsXX", "dataType": "int64"}, {"expression": "ga:uniquePageviews", "alias": "ga_uniquePageviews", "dataType": "int64"}, {"expression": "ga:timeOnPage", "alias": "ga_timeOnPage", "dataType": "float"}, {"expression": "ga:avgTimeOnPage", "alias": "ga_avgTimeOnPage", "dataType": "float"}, {"expression": "ga:exits", "alias": "ga_exits", "dataType": "int64"}, {"expression": "ga:exitRate", "alias": "ga_exitRate", "dataType": "float"}, {"expression": "ga:searchResultViews", "alias": "ga_searchResultViews", "dataType": "int64"}, {"expression": "ga:searchUniques", "alias": "ga_searchUniques", "dataType": "int64"}, {"expression": "ga:avgSearchResultViews", "alias": "ga_avgSearchResultViews", "dataType": "float"}, {"expression": "ga:searchSessions", "alias": "ga_searchSessions", "dataType": "int64"}, {"expression": "ga:searchVisits", "alias": "ga_searchVisits", "dataType": "int64"}, {"expression": "ga:percentSessionsWithSearch", "alias": "ga_percentSessionsWithSearch", "dataType": "float"}, {"expression": "ga:percentVisitsWithSearch", "alias": "ga_percentVisitsWithSearch", "dataType": "float"}, {"expression": "ga:searchDepth", "alias": "ga_searchDepth", "dataType": "int64"}, {"expression": "ga:avgSearchDepth", "alias": "ga_avgSearchDepth", "dataType": "float"}, {"expression": "ga:searchRefinements", "alias": "ga_searchRefinements", "dataType": "int64"}, {"expression": "ga:percentSearchRefinements", "alias": "ga_percentSearchRefinements", "dataType": "float"}, {"expression": "ga:searchDuration", "alias": "ga_searchDuration", "dataType": "float"}, {"expression": "ga:avgSearchDuration", "alias": "ga_avgSearchDuration", "dataType": "float"}, {"expression": "ga:searchExits", "alias": "ga_searchExits", "dataType": "int64"}, {"expression": "ga:searchExitRate", "alias": "ga_searchExitRate", "dataType": "float"}, {"expression": "ga:searchGoalXXConversionRate", "alias": "ga_searchGoalXXConversionRate", "dataType": "float"}, {"expression": "ga:searchGoalConversionRateAll", "alias": "ga_searchGoalConversionRateAll", "dataType": "float"}, {"expression": "ga:goalValueAllPerSearch", "alias": "ga_goalValueAllPerSearch", "dataType": "float"}, {"expression": "ga:pageLoadTime", "alias": "ga_pageLoadTime", "dataType": "int64"}, {"expression": "ga:pageLoadSample", "alias": "ga_pageLoadSample", "dataType": "int64"}, {"expression": "ga:avgPageLoadTime", "alias": "ga_avgPageLoadTime", "dataType": "float"}, {"expression": "ga:domainLookupTime", "alias": "ga_domainLookupTime", "dataType": "int64"}, {"expression": "ga:avgDomainLookupTime", "alias": "ga_avgDomainLookupTime", "dataType": "float"}, {"expression": "ga:pageDownloadTime", "alias": "ga_pageDownloadTime", "dataType": "int64"}, {"expression": "ga:avgPageDownloadTime", "alias": "ga_avgPageDownloadTime", "dataType": "float"}, {"expression": "ga:redirectionTime", "alias": "ga_redirectionTime", "dataType": "int64"}, {"expression": "ga:avgRedirectionTime", "alias": "ga_avgRedirectionTime", "dataType": "float"}, {"expression": "ga:serverConnectionTime", "alias": "ga_serverConnectionTime", "dataType": "int64"}, {"expression": "ga:avgServerConnectionTime", "alias": "ga_avgServerConnectionTime", "dataType": "float"}, {"expression": "ga:serverResponseTime", "alias": "ga_serverResponseTime", "dataType": "int64"}, {"expression": "ga:avgServerResponseTime", "alias": "ga_avgServerResponseTime", "dataType": "float"}, {"expression": "ga:speedMetricsSample", "alias": "ga_speedMetricsSample", "dataType": "int64"}, {"expression": "ga:domInteractiveTime", "alias": "ga_domInteractiveTime", "dataType": "int64"}, {"expression": "ga:avgDomInteractiveTime", "alias": "ga_avgDomInteractiveTime", "dataType": "float"}, {"expression": "ga:domContentLoadedTime", "alias": "ga_domContentLoadedTime", "dataType": "int64"}, {"expression": "ga:avgDomContentLoadedTime", "alias": "ga_avgDomContentLoadedTime", "dataType": "float"}, {"expression": "ga:domLatencyMetricsSample", "alias": "ga_domLatencyMetricsSample", "dataType": "int64"}, {"expression": "ga:screenviews", "alias": "ga_screenviews", "dataType": "int64"}, {"expression": "ga:uniqueScreenviews", "alias": "ga_uniqueScreenviews", "dataType": "int64"}, {"expression": "ga:uniqueAppviews", "alias": "ga_uniqueAppviews", "dataType": "int64"}, {"expression": "ga:screenviewsPerSession", "alias": "ga_screenviewsPerSession", "dataType": "float"}, {"expression": "ga:timeOnScreen", "alias": "ga_timeOnScreen", "dataType": "float"}, {"expression": "ga:avgScreenviewDuration", "alias": "ga_avgScreenviewDuration", "dataType": "float"}, {"expression": "ga:totalEvents", "alias": "ga_totalEvents", "dataType": "int64"}, {"expression": "ga:uniqueDimensionCombinations", "alias": "ga_uniqueDimensionCombinations", "dataType": "int64"}, {"expression": "ga:uniqueEvents", "alias": "ga_uniqueEvents", "dataType": "int64"}, {"expression": "ga:eventValue", "alias": "ga_eventValue", "dataType": "int64"}, {"expression": "ga:avgEventValue", "alias": "ga_avgEventValue", "dataType": "float"}, {"expression": "ga:sessionsWithEvent", "alias": "ga_sessionsWithEvent", "dataType": "int64"}, {"expression": "ga:visitsWithEvent", "alias": "ga_visitsWithEvent", "dataType": "int64"}, {"expression": "ga:eventsPerSessionWithEvent", "alias": "ga_eventsPerSessionWithEvent", "dataType": "float"}, {"expression": "ga:eventsPerVisitWithEvent", "alias": "ga_eventsPerVisitWithEvent", "dataType": "float"}, {"expression": "ga:transactions", "alias": "ga_transactions", "dataType": "int64"}, {"expression": "ga:transactionsPerSession", "alias": "ga_transactionsPerSession", "dataType": "float"}, {"expression": "ga:transactionsPerVisit", "alias": "ga_transactionsPerVisit", "dataType": "float"}, {"expression": "ga:transactionRevenue", "alias": "ga_transactionRevenue", "dataType": "float"}, {"expression": "ga:revenuePerTransaction", "alias": "ga_revenuePerTransaction", "dataType": "float"}, {"expression": "ga:transactionRevenuePerSession", "alias": "ga_transactionRevenuePerSession", "dataType": "float"}, {"expression": "ga:transactionRevenuePerVisit", "alias": "ga_transactionRevenuePerVisit", "dataType": "float"}, {"expression": "ga:transactionShipping", "alias": "ga_transactionShipping", "dataType": "float"}, {"expression": "ga:transactionTax", "alias": "ga_transactionTax", "dataType": "float"}, {"expression": "ga:totalValue", "alias": "ga_totalValue", "dataType": "float"}, {"expression": "ga:itemQuantity", "alias": "ga_itemQuantity", "dataType": "int64"}, {"expression": "ga:uniquePurchases", "alias": "ga_uniquePurchases", "dataType": "int64"}, {"expression": "ga:revenuePerItem", "alias": "ga_revenuePerItem", "dataType": "float"}, {"expression": "ga:itemRevenue", "alias": "ga_itemRevenue", "dataType": "float"}, {"expression": "ga:itemsPerPurchase", "alias": "ga_itemsPerPurchase", "dataType": "float"}, {"expression": "ga:localTransactionRevenue", "alias": "ga_localTransactionRevenue", "dataType": "float"}, {"expression": "ga:localTransactionShipping", "alias": "ga_localTransactionShipping", "dataType": "float"}, {"expression": "ga:localTransactionTax", "alias": "ga_localTransactionTax", "dataType": "float"}, {"expression": "ga:localItemRevenue", "alias": "ga_localItemRevenue", "dataType": "float"}, {"expression": "ga:socialInteractions", "alias": "ga_socialInteractions", "dataType": "int64"}, {"expression": "ga:uniqueSocialInteractions", "alias": "ga_uniqueSocialInteractions", "dataType": "int64"}, {"expression": "ga:socialInteractionsPerSession", "alias": "ga_socialInteractionsPerSession", "dataType": "float"}, {"expression": "ga:socialInteractionsPerVisit", "alias": "ga_socialInteractionsPerVisit", "dataType": "float"}, {"expression": "ga:userTimingValue", "alias": "ga_userTimingValue", "dataType": "int64"}, {"expression": "ga:userTimingSample", "alias": "ga_userTimingSample", "dataType": "int64"}, {"expression": "ga:avgUserTimingValue", "alias": "ga_avgUserTimingValue", "dataType": "float"}, {"expression": "ga:exceptions", "alias": "ga_exceptions", "dataType": "int64"}, {"expression": "ga:exceptionsPerScreenview", "alias": "ga_exceptionsPerScreenview", "dataType": "float"}, {"expression": "ga:fatalExceptions", "alias": "ga_fatalExceptions", "dataType": "int64"}, {"expression": "ga:fatalExceptionsPerScreenview", "alias": "ga_fatalExceptionsPerScreenview", "dataType": "float"}, {"expression": "ga:metricXX", "alias": "ga_metricXX", "dataType": "int64"}, {"expression": "ga:dcmFloodlightQuantity", "alias": "ga_dcmFloodlightQuantity", "dataType": "int64"}, {"expression": "ga:dcmFloodlightRevenue", "alias": "ga_dcmFloodlightRevenue", "dataType": "float"}, {"expression": "ga:adsenseRevenue", "alias": "ga_adsenseRevenue", "dataType": "float"}, {"expression": "ga:adsenseAdUnitsViewed", "alias": "ga_adsenseAdUnitsViewed", "dataType": "int64"}, {"expression": "ga:adsenseAdsViewed", "alias": "ga_adsenseAdsViewed", "dataType": "int64"}, {"expression": "ga:adsenseAdsClicks", "alias": "ga_adsenseAdsClicks", "dataType": "int64"}, {"expression": "ga:adsensePageImpressions", "alias": "ga_adsensePageImpressions", "dataType": "int64"}, {"expression": "ga:adsenseCTR", "alias": "ga_adsenseCTR", "dataType": "float"}, {"expression": "ga:adsenseECPM", "alias": "ga_adsenseECPM", "dataType": "float"}, {"expression": "ga:adsenseExits", "alias": "ga_adsenseExits", "dataType": "int64"}, {"expression": "ga:adsenseViewableImpressionPercent", "alias": "ga_adsenseViewableImpressionPercent", "dataType": "float"}, {"expression": "ga:adsenseCoverage", "alias": "ga_adsenseCoverage", "dataType": "float"}, {"expression": "ga:totalPublisherImpressions", "alias": "ga_totalPublisherImpressions", "dataType": "int64"}, {"expression": "ga:totalPublisherCoverage", "alias": "ga_totalPublisherCoverage", "dataType": "float"}, {"expression": "ga:totalPublisherMonetizedPageviews", "alias": "ga_totalPublisherMonetizedPageviews", "dataType": "int64"}, {"expression": "ga:totalPublisherImpressionsPerSession", "alias": "ga_totalPublisherImpressionsPerSession", "dataType": "float"}, {"expression": "ga:totalPublisherViewableImpressionsPercent", "alias": "ga_totalPublisherViewableImpressionsPercent", "dataType": "float"}, {"expression": "ga:totalPublisherClicks", "alias": "ga_totalPublisherClicks", "dataType": "int64"}, {"expression": "ga:totalPublisherCTR", "alias": "ga_totalPublisherCTR", "dataType": "float"}, {"expression": "ga:totalPublisherRevenue", "alias": "ga_totalPublisherRevenue", "dataType": "float"}, {"expression": "ga:totalPublisherRevenuePer1000Sessions", "alias": "ga_totalPublisherRevenuePer1000Sessions", "dataType": "float"}, {"expression": "ga:totalPublisherECPM", "alias": "ga_totalPublisherECPM", "dataType": "float"}, {"expression": "ga:adxImpressions", "alias": "ga_adxImpressions", "dataType": "int64"}, {"expression": "ga:adxCoverage", "alias": "ga_adxCoverage", "dataType": "float"}, {"expression": "ga:adxMonetizedPageviews", "alias": "ga_adxMonetizedPageviews", "dataType": "int64"}, {"expression": "ga:adxImpressionsPerSession", "alias": "ga_adxImpressionsPerSession", "dataType": "float"}, {"expression": "ga:adxViewableImpressionsPercent", "alias": "ga_adxViewableImpressionsPercent", "dataType": "float"}, {"expression": "ga:adxClicks", "alias": "ga_adxClicks", "dataType": "int64"}, {"expression": "ga:adxCTR", "alias": "ga_adxCTR", "dataType": "float"}, {"expression": "ga:adxRevenue", "alias": "ga_adxRevenue", "dataType": "float"}, {"expression": "ga:adxRevenuePer1000Sessions", "alias": "ga_adxRevenuePer1000Sessions", "dataType": "float"}, {"expression": "ga:adxECPM", "alias": "ga_adxECPM", "dataType": "float"}, {"expression": "ga:dfpImpressions", "alias": "ga_dfpImpressions", "dataType": "int64"}, {"expression": "ga:dfpCoverage", "alias": "ga_dfpCoverage", "dataType": "float"}, {"expression": "ga:dfpMonetizedPageviews", "alias": "ga_dfpMonetizedPageviews", "dataType": "int64"}, {"expression": "ga:dfpImpressionsPerSession", "alias": "ga_dfpImpressionsPerSession", "dataType": "float"}, {"expression": "ga:dfpViewableImpressionsPercent", "alias": "ga_dfpViewableImpressionsPercent", "dataType": "float"}, {"expression": "ga:dfpClicks", "alias": "ga_dfpClicks", "dataType": "int64"}, {"expression": "ga:dfpCTR", "alias": "ga_dfpCTR", "dataType": "float"}, {"expression": "ga:dfpRevenue", "alias": "ga_dfpRevenue", "dataType": "float"}, {"expression": "ga:dfpRevenuePer1000Sessions", "alias": "ga_dfpRevenuePer1000Sessions", "dataType": "float"}, {"expression": "ga:dfpECPM", "alias": "ga_dfpECPM", "dataType": "float"}, {"expression": "ga:backfillImpressions", "alias": "ga_backfillImpressions", "dataType": "int64"}, {"expression": "ga:backfillCoverage", "alias": "ga_backfillCoverage", "dataType": "float"}, {"expression": "ga:backfillMonetizedPageviews", "alias": "ga_backfillMonetizedPageviews", "dataType": "int64"}, {"expression": "ga:backfillImpressionsPerSession", "alias": "ga_backfillImpressionsPerSession", "dataType": "float"}, {"expression": "ga:backfillViewableImpressionsPercent", "alias": "ga_backfillViewableImpressionsPercent", "dataType": "float"}, {"expression": "ga:backfillClicks", "alias": "ga_backfillClicks", "dataType": "int64"}, {"expression": "ga:backfillCTR", "alias": "ga_backfillCTR", "dataType": "float"}, {"expression": "ga:backfillRevenue", "alias": "ga_backfillRevenue", "dataType": "float"}, {"expression": "ga:backfillRevenuePer1000Sessions", "alias": "ga_backfillRevenuePer1000Sessions", "dataType": "float"}, {"expression": "ga:backfillECPM", "alias": "ga_backfillECPM", "dataType": "float"}, {"expression": "ga:buyToDetailRate", "alias": "ga_buyToDetailRate", "dataType": "float"}, {"expression": "ga:calcMetric_<NAME>", "alias": "ga_calcMetric_<NAME>", "dataType": "int64"}, {"expression": "ga:cartToDetailRate", "alias": "ga_cartToDetailRate", "dataType": "float"}, {"expression": "ga:cohortActiveUsers", "alias": "ga_cohortActiveUsers", "dataType": "int64"}, {"expression": "ga:cohortAppviewsPerUser", "alias": "ga_cohortAppviewsPerUser", "dataType": "float"}, {"expression": "ga:cohortAppviewsPerUserWithLifetimeCriteria", "alias": "ga_cohortAppviewsPerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortGoalCompletionsPerUser", "alias": "ga_cohortGoalCompletionsPerUser", "dataType": "float"}, {"expression": "ga:cohortGoalCompletionsPerUserWithLifetimeCriteria", "alias": "ga_cohortGoalCompletionsPerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortPageviewsPerUser", "alias": "ga_cohortPageviewsPerUser", "dataType": "float"}, {"expression": "ga:cohortPageviewsPerUserWithLifetimeCriteria", "alias": "ga_cohortPageviewsPerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortRetentionRate", "alias": "ga_cohortRetentionRate", "dataType": "float"}, {"expression": "ga:cohortRevenuePerUser", "alias": "ga_cohortRevenuePerUser", "dataType": "float"}, {"expression": "ga:cohortRevenuePerUserWithLifetimeCriteria", "alias": "ga_cohortRevenuePerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortSessionDurationPerUser", "alias": "ga_cohortSessionDurationPerUser", "dataType": "float"}, {"expression": "ga:cohortSessionDurationPerUserWithLifetimeCriteria", "alias": "ga_cohortSessionDurationPerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortSessionsPerUser", "alias": "ga_cohortSessionsPerUser", "dataType": "float"}, {"expression": "ga:cohortSessionsPerUserWithLifetimeCriteria", "alias": "ga_cohortSessionsPerUserWithLifetimeCriteria", "dataType": "float"}, {"expression": "ga:cohortTotalUsers", "alias": "ga_cohortTotalUsers", "dataType": "int64"}, {"expression": "ga:cohortTotalUsersWithLifetimeCriteria", "alias": "ga_cohortTotalUsersWithLifetimeCriteria", "dataType": "int64"}, {"expression": "ga:dbmCPA", "alias": "ga_dbmCPA", "dataType": "float"}, {"expression": "ga:dbmCPC", "alias": "ga_dbmCPC", "dataType": "float"}, {"expression": "ga:dbmCPM", "alias": "ga_dbmCPM", "dataType": "float"}, {"expression": "ga:dbmCTR", "alias": "ga_dbmCTR", "dataType": "float"}, {"expression": "ga:dbmClicks", "alias": "ga_dbmClicks", "dataType": "int64"}, {"expression": "ga:dbmConversions", "alias": "ga_dbmConversions", "dataType": "int64"}, {"expression": "ga:dbmCost", "alias": "ga_dbmCost", "dataType": "float"}, {"expression": "ga:dbmImpressions", "alias": "ga_dbmImpressions", "dataType": "int64"}, {"expression": "ga:dbmROAS", "alias": "ga_dbmROAS", "dataType": "float"}, {"expression": "ga:dcmCPC", "alias": "ga_dcmCPC", "dataType": "float"}, {"expression": "ga:dcmCTR", "alias": "ga_dcmCTR", "dataType": "float"}, {"expression": "ga:dcmClicks", "alias": "ga_dcmClicks", "dataType": "int64"}, {"expression": "ga:dcmCost", "alias": "ga_dcmCost", "dataType": "float"}, {"expression": "ga:dcmImpressions", "alias": "ga_dcmImpressions", "dataType": "int64"}, {"expression": "ga:dcmMargin", "alias": "ga_dcmMargin", "dataType": "float"}, {"expression": "ga:dcmROAS", "alias": "ga_dcmROAS", "dataType": "float"}, {"expression": "ga:dcmRPC", "alias": "ga_dcmRPC", "dataType": "float"}, {"expression": "ga:dsCPC", "alias": "ga_dsCPC", "dataType": "float"}, {"expression": "ga:dsCTR", "alias": "ga_dsCTR", "dataType": "float"}, {"expression": "ga:dsClicks", "alias": "ga_dsClicks", "dataType": "int64"}, {"expression": "ga:dsCost", "alias": "ga_dsCost", "dataType": "float"}, {"expression": "ga:dsImpressions", "alias": "ga_dsImpressions", "dataType": "int64"}, {"expression": "ga:dsProfit", "alias": "ga_dsProfit", "dataType": "float"}, {"expression": "ga:dsReturnOnAdSpend", "alias": "ga_dsReturnOnAdSpend", "dataType": "float"}, {"expression": "ga:dsRevenuePerClick", "alias": "ga_dsRevenuePerClick", "dataType": "float"}, {"expression": "ga:hits", "alias": "ga_hits", "dataType": "int64"}, {"expression": "ga:internalPromotionCTR", "alias": "ga_internalPromotionCTR", "dataType": "float"}, {"expression": "ga:internalPromotionClicks", "alias": "ga_internalPromotionClicks", "dataType": "int64"}, {"expression": "ga:internalPromotionViews", "alias": "ga_internalPromotionViews", "dataType": "int64"}, {"expression": "ga:localProductRefundAmount", "alias": "ga_localProductRefundAmount", "dataType": "float"}, {"expression": "ga:localRefundAmount", "alias": "ga_localRefundAmount", "dataType": "float"}, {"expression": "ga:productAddsToCart", "alias": "ga_productAddsToCart", "dataType": "int64"}, {"expression": "ga:productCheckouts", "alias": "ga_productCheckouts", "dataType": "int64"}, {"expression": "ga:productDetailViews", "alias": "ga_productDetailViews", "dataType": "int64"}, {"expression": "ga:productListCTR", "alias": "ga_productListCTR", "dataType": "float"}, {"expression": "ga:productListClicks", "alias": "ga_productListClicks", "dataType": "int64"}, {"expression": "ga:productListViews", "alias": "ga_productListViews", "dataType": "int64"}, {"expression": "ga:productRefundAmount", "alias": "ga_productRefundAmount", "dataType": "float"}, {"expression": "ga:productRefunds", "alias": "ga_productRefunds", "dataType": "int64"}, {"expression": "ga:productRemovesFromCart", "alias": "ga_productRemovesFromCart", "dataType": "int64"}, {"expression": "ga:productRevenuePerPurchase", "alias": "ga_productRevenuePerPurchase", "dataType": "float"}, {"expression": "ga:quantityAddedToCart", "alias": "ga_quantityAddedToCart", "dataType": "int64"}, {"expression": "ga:quantityCheckedOut", "alias": "ga_quantityCheckedOut", "dataType": "int64"}, {"expression": "ga:quantityRefunded", "alias": "ga_quantityRefunded", "dataType": "int64"}, {"expression": "ga:quantityRemovedFromCart", "alias": "ga_quantityRemovedFromCart", "dataType": "int64"}, {"expression": "ga:refundAmount", "alias": "ga_refundAmount", "dataType": "float"}, {"expression": "ga:revenuePerUser", "alias": "ga_revenuePerUser", "dataType": "float"}, {"expression": "ga:sessionsPer<PERSON>ser", "alias": "ga_sessionsPerUser", "dataType": "float"}, {"expression": "ga:totalRefunds", "alias": "ga_totalRefunds", "dataType": "int64"}, {"expression": "ga:transactionsPerUser", "alias": "ga_transactionsPerUser", "dataType": "float"}]