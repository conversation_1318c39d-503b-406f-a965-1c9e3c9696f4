import { TabWidget, Widget, WidgetId } from '@core/common/domain';

export class ReferenceCheck {
  private readonly widget: Widget;

  constructor(widget: Widget) {
    this.widget = widget;
  }

  /**
   * Checks if the widget has a reference to the provided widget IDs.
   *
   * @param {Record<WidgetId, WidgetId>} reference - A mapping of widget IDs to be checked against.
   * @return {boolean} Returns true if there is at least one matching widget ID in the reference; otherwise, false.
   */
  hasReference(reference: Record<WidgetId, WidgetId>): boolean {
    if (TabWidget.isTabWidget(this.widget)) {
      const tabWidget: TabWidget = this.widget as TabWidget;
      const oldWidgetIds = Object.values(reference);

      for (const tab of tabWidget.tabItems) {
        for (const widgetId of tab.widgetIds) {
          if (oldWidgetIds.includes(widgetId)) {
            return true;
          }
        }
      }
    }

    return false;
  }

  /**
   * Updates the current widget by replacing widget IDs within the tab items
   * based on the provided reference mapping.
   *
   * @param {Record<WidgetId, WidgetId>} reference - A mapping of new widget IDs to old widget IDs.
   * @return {Widget} The updated widget object. If the current widget is not a TabWidget or the reference
   * does not apply, the original widget is returned unchanged.
   */
  update(reference: Record<WidgetId, WidgetId>): Widget {
    if (!TabWidget.isTabWidget(this.widget)) {
      return this.widget;
    }

    if (!this.hasReference(reference)) {
      return this.widget;
    }

    const tabWidget = this.widget as TabWidget;

    const updatedTabWidget = TabWidget.fromObject(tabWidget);

    for (let i = 0; i < updatedTabWidget.tabItems.length; i++) {
      const tab = updatedTabWidget.tabItems[i];
      const updatedWidgetIds: WidgetId[] = [];

      for (const widgetId of tab.widgetIds) {
        let updated = false;

        for (const [newId, oldId] of Object.entries(reference)) {
          if (widgetId === oldId) {
            updatedWidgetIds.push(newId as WidgetId);
            updated = true;
            break;
          }
        }
        if (!updated) {
          updatedWidgetIds.push(widgetId);
        }
      }

      tab.widgetIds = updatedWidgetIds;
    }

    return updatedTabWidget;
  }
}

export class ReferenceHelper {
  static getReferenceWidgets(widget: Widget[]): Record<WidgetId, WidgetId> {
    return widget.reduce((acc, widget) => {
      if (widget.extraData?.origin) {
        acc[widget.id] = widget.extraData?.origin;
      }
      return acc;
    }, {} as Record<WidgetId, WidgetId>);
  }

  static updateReferenceWidgets(widget: Widget[], referenceWidgets: Record<WidgetId, WidgetId>): Widget[] {
    return widget.map(widget => {
      if (new ReferenceCheck(widget).hasReference(referenceWidgets)) {
        return new ReferenceCheck(widget).update(referenceWidgets);
      }

      return widget;
    });
  }
}
