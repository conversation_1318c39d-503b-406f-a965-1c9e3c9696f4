<template>
  <LayoutWrapper ref="layoutWrapper">
    <LayoutSidebar :items="navItems">
      <template v-slot:top>
        <DiShadowButton id="create-directory" class="create-directory mb-0" title="New"
                        @click="e => showMenuCreateDirectory('create-directory')">
          <i class="di-icon-add"></i>
        </DiShadowButton>
      </template>
    </LayoutSidebar>
    <router-view ref="myData" class="my-data-listing"></router-view>
    <ContextMenu ref="diContextMenu" :ignoreOutsideClass="listIgnoreClassForContextMenu" minWidth="210px"
                 textColor="var(--text-color)" />
    <DiRenameModal ref="createDirectoryModal" title="Create Folder" label="Folder name" placeholder="Type folder name"
                   action-name="Create" />
    <DiRenameModal ref="createDashboardModal" title="Create Dashboard" label="Dashboard name"
                   placeholder="Type dashboard name" action-name="Create" />
    <DirectoryRename ref="mdRenameDirectory" />
    <DiShareModal ref="mdShareDirectory" @shared="reload" />

    <MyDataPickDirectory ref="directoryPicker" @selectDirectory="handleMoveToDirectory" />
    <MyDataPickDirectory ref="copyDashboardPicker" @selectDirectory="copy" />
    <PasswordModal ref="passwordModal"></PasswordModal>
  </LayoutWrapper>
</template>

<script lang="ts">
import router from '@/router/Router';
import PasswordModal from '@/screens/dashboard-detail/components/PasswordModal.vue';
import DirectoryRename from '@/screens/directory/components/DirectoryRename.vue';
import { DirectoryModule } from '@/screens/directory/store/DirectoryStore';
import { DefaultDirectoryId } from '@/screens/directory/views/mydata/DefaultDirectoryId';
import MyData from '@/screens/directory/views/mydata/MyData.vue';
import MyDataPickDirectory from '@/screens/lake-house/components/move-file/MyDataPickDirectory.vue';
import { ContextMenuItem, Routers, Status } from '@/shared';
import { Track } from '@/shared/anotation';
import DiShareModal from '@/shared/components/common/di-share-modal/DiShareModal.vue';
import NavigationPanel, { NavigationItem } from '@/shared/components/common/NavigationPanel.vue';
import PopoverV2 from '@/shared/components/common/popover-v2/PopoverV2.vue';
import ContextMenu from '@/shared/components/ContextMenu.vue';
import DiRenameModal from '@/shared/components/DiRenameModal.vue';
import { LayoutSidebar, LayoutWrapper } from '@/shared/components/layout-wrapper';
import { RouterEnteringHook } from '@/shared/components/vue-hook/RouterEnteringHook';
import { Modals } from '@/utils/Modals';
import { PopupUtils } from '@/utils/PopupUtils';
import { RouterUtils } from '@/utils/RouterUtils';
import {
  CreateDashboardRequest,
  CreateDirectoryRequest,
  Dashboard,
  DashboardId,
  DIException,
  Directory,
  DirectoryId,
  DirectoryType
} from '@core/common/domain';
import { Di } from '@core/common/modules';
import { DashboardService } from '@core/common/services';
import { TrackEvents } from '@core/tracking/enum/TrackEvents';
import { Log } from '@core/utils';
import { Component, Ref, Vue } from 'vue-property-decorator';
import { Route } from 'vue-router';
import { NavigationGuardNext } from 'vue-router/types/router';

export enum DirectoryListingEvents {
  ShowMenuCreateDirectory = 'show-menu-create-directory',
  ShowShareModal = 'show-share-modal',
  ShowMenuSettingDirectory = 'show-menu-setting-directory',
  RestoreDirectory = 'restore-directory'
}

@Component({
  components :{
    NavigationPanel,
    DirectoryRename,
    DiShareModal,
    LayoutWrapper,
    LayoutSidebar,
    PopoverV2,
    MyDataPickDirectory,
    PasswordModal
  }
})
export default class DirectoryListing extends Vue implements RouterEnteringHook {
  protected readonly trackEvents = TrackEvents;

  protected listIgnoreClassForContextMenu = ['di-icon-setting', 'create-directory'];

  @Ref()
  protected diContextMenu!: ContextMenu;

  @Ref()
  protected createDirectoryModal!: DiRenameModal;

  @Ref()
  protected createDashboardModal!: DiRenameModal;

  @Ref()
  protected mdRenameDirectory!: DirectoryRename;

  @Ref()
  protected mdShareDirectory!: DiShareModal;

  @Ref()
  protected readonly passwordModal!: PasswordModal;

  @Ref()
  protected readonly myData?: MyData;

  @Ref()
  protected readonly layoutWrapper!: LayoutWrapper;

  @Ref()
  protected readonly directoryPicker!: MyDataPickDirectory;

  @Ref()
  protected readonly copyDashboardPicker!: MyDataPickDirectory;

  protected get navItems(): NavigationItem[] {
    return [
      {
        id :'mydata',
        displayName :'All Data',
        icon :'di-icon-my-data',
        to :'/mydata'
      },
      {
        id :'shared-with-me',
        displayName :'Shared With Me',
        icon :'di-icon-share-with-me',
        to :'/shared'
      },
      {
        id :'recent',
        displayName :'Recent',
        icon :'di-icon-recent',
        to :'/recent'
      },
      {
        id :'starred',
        displayName :'Starred',
        icon :'di-icon-star',
        to :'/starred'
      },
      {
        id :'trash',
        displayName :'Trash',
        icon :'di-icon-delete',
        to :'/trash'
      }
    ];
  }

  protected get parentId() {
    return RouterUtils.parseToParamInfo(this.$route.params.name).idAsNumber() || DefaultDirectoryId.MyData;
  }

  beforeRouteEnter(to: Route, from: Route, next: NavigationGuardNext<any>) {
    if (RouterUtils.isLogin() || RouterUtils.getToken(to)) {
      next();
    } else {
      next({ name :Routers.Login });
    }
  }

  mounted() {
    this.$root.$on(DirectoryListingEvents.ShowShareModal, this.showShareModal);
    this.$root.$on(DirectoryListingEvents.ShowMenuSettingDirectory, this.showMenuSettingDirectory);
  }

  beforeDestroy() {
    this.$root.$off(DirectoryListingEvents.ShowShareModal, this.showShareModal);
    this.$root.$off(DirectoryListingEvents.ShowMenuSettingDirectory, this.showMenuSettingDirectory);
  }

  showMenuCreateDirectory(id: string) {
    const items = this.getMenuOptions(this.parentId);
    this.diContextMenu.showAt(id, items, {
      paddingTop :8
    });
  }

  showMenuSettingDirectory(item: Directory, routerName: Routers) {
    // this.tblDirectoryListing.selectRow(index);
    const menuItems = this.getDirectoryMenuItem(item, routerName).filter(item => !item?.hidden);
    this.diContextMenu.show(event, menuItems);
  }

  protected showShareModal(item: Directory) {
    this.diContextMenu.hide();

    switch (item.directoryType) {
      case DirectoryType.Directory:
        this.mdShareDirectory.showShareDirectory(item);
        break;
      case DirectoryType.Query:
        this.mdShareDirectory.showShareAdhocQuery(item);
        break;
      case DirectoryType.Dashboard:
        this.mdShareDirectory.showShareDashboard(item);
        break;
    }
    Log.debug('Share::item', item);
  }

  @Track(TrackEvents.MyDataDirectoryRename, { directory :(_: DirectoryListing, args: any) => args[0] })
  protected showRenameModal(item: Directory) {
    this.diContextMenu.hide();
    this.mdRenameDirectory.show(item);
  }

  @Track(TrackEvents.DuplicateDirectory, { directory :(_: DirectoryListing, args: any) => args[0] })
  protected async duplicate(item: Directory) {
    try {
      this.diContextMenu.hide();
      DirectoryModule.setStatus(Status.Updating);
      switch (item.directoryType) {
        case DirectoryType.Dashboard:
        case DirectoryType.Query: {
          this.passwordModal.requirePassword(item, item.owner?.username || item.ownerId, async () => {
            await this.duplicateDashboard(item, this.parentId);
            await this.reload();
            // DirectoryModule.setStatus(Status.Loaded);
          });

          DirectoryModule.setStatus(Status.Loaded);
          break;
        }
        default: {
          Log.error(`Unsupported duplicate type ${item.directoryType}`);
          DirectoryModule.setStatus(Status.Loaded);
          PopupUtils.showError('Unsupported duplicate data');
        }
      }
    } catch (ex) {
      Log.error('Duplicate::error', ex);
      PopupUtils.showError(`Duplicate failed cause ${ex.message}`);
      DirectoryModule.setStatus(Status.Loaded);
    }
  }

  protected async duplicateDashboard(item: Directory, parentId: DashboardId): Promise<Dashboard> {
    if (!item.dashboardId) {
      return Promise.reject(new DIException('Dashboard id is not found'));
    }

    const dashboard = await Di.get<DashboardService>(DashboardService).get(item.dashboardId);
    const duplicatedDashboard: Dashboard = await DirectoryModule.createDashboard(CreateDashboardRequest.fromDashboard(item.directoryType, parentId, dashboard));
    return await DirectoryModule.migrateReferenceDashboard(duplicatedDashboard);
  }

  protected showMoveModal(e: Event, directory: Directory) {
    this.directoryPicker.show(e, [directory.id], directory.id);
  }

  protected async softDelete(item: Directory) {
    try {
      await DirectoryModule.softDelete(item.id);
    } catch (err) {
      PopupUtils.showError(err.message);
    }
  }

  protected getDirectoryMenuItem(directory: Directory, routerName: Routers): ContextMenuItem[] {
    return [
      {
        text :'Rename',
        click :() => {
          this.showRenameModal(directory);
        },
        icon :'di-icon-rename'
      },
      {
        text :'Duplicate',
        click :() => {
          this.duplicate(directory);
        },
        hidden :directory.directoryType === DirectoryType.Directory,
        icon :'di-icon-duplicate'
      },
      {
        text :'Copy',
        click :(event: MouseEvent) => {
          this.copyDashboardPicker.show(event, [directory.id], directory);
        },
        hidden :directory.directoryType === DirectoryType.Directory,
        icon :'di-icon-copy-2'
      },
      {
        text :'Move to',
        hidden :routerName !== Routers.AllData,
        click :(e: Event) => {
          this.showMoveModal(e, directory);
        },
        icon :'di-icon-move-folder'
      },
      this.getStarMenuItem(directory),
      {
        text :'Remove',
        click :() => {
          this.confirmDeleteDirectory(directory);
        },
        icon :'di-icon-delete'
      }
    ];
  }

  protected getStarMenuItem(directory: Directory): ContextMenuItem {
    if (directory.isStarred) {
      return {
        text :'Remove from Starred',
        click :() => {
          this.diContextMenu.hide();
          this.removeStar(directory);
        },
        icon :'di-icon-add-to-star-converted'
      };
    } else {
      return {
        text :'Add to Starred',
        click :() => {
          this.diContextMenu.hide();
          this.star(directory);
        },
        icon :'di-icon-add-to-star-converted'
      };
    }
  }

  protected showCreateDashboardModal(parentId?: DirectoryId) {
    this.layoutWrapper.toggleSidebar(false);
    this.diContextMenu.hide();
    this.createDashboardModal.show('', async (name: string) => {
      try {
        this.createDashboardModal.setLoading(true);
        const request = CreateDashboardRequest.createDashboardRequest({ name :name, parentDirectoryId :parentId || 0 });
        const dashboard = await DirectoryModule.createDashboard(request);
        this.createDashboardModal.hide();
        this.createDashboardModal.setLoading(false);
        this.navigateToDashboard(dashboard.id, dashboard.name);
      } catch (ex) {
        Log.error('CreateDashboard::error', ex);
        this.createDashboardModal.setError(ex.message);
        this.createDashboardModal.setLoading(false);
      }
    });
  }

  protected async navigateToDashboard(dashboardId: number, name: string): Promise<void> {
    await RouterUtils.to(Routers.Dashboard, {
      params :{
        name :RouterUtils.buildParamPath(dashboardId, name)
      },
      query :{
        token :RouterUtils.getToken(router.currentRoute)
      }
    });
  }

  protected showCreateDirectoryModal(parentId?: DirectoryId) {
    this.layoutWrapper.toggleSidebar(false);
    this.diContextMenu.hide();
    const newDirectory = new CreateDirectoryRequest({
      isRemoved :false,
      parentId :parentId || 0,
      directoryType :DirectoryType.Directory
    });
    this.createDirectoryModal.show('', async (newName: string) => {
      try {
        this.createDirectoryModal.setLoading(true);
        newDirectory.name = newName;
        const directory = await DirectoryModule.createFolder(newDirectory);
        this.createDirectoryModal.hide();
        this.createDirectoryModal.setLoading(false);
        this.navigateToDirectory(directory.id, directory.name);
      } catch (ex) {
        Log.error('CreateDirectory::error', ex);
        this.createDirectoryModal.setError(ex.message);
        this.createDirectoryModal.setLoading(false);
      }
    });
  }

  protected async navigateToDirectory(directoryId: number, name: string): Promise<void> {
    await RouterUtils.to(Routers.AllData, {
      name :Routers.AllData,
      params :{
        name :RouterUtils.buildParamPath(directoryId, name)
      },
      query :{
        token :RouterUtils.getToken(router.currentRoute)
      }
    });
  }

  protected getMenuOptions(parentId?: DirectoryId): ContextMenuItem[] {
    return [
      {
        icon :'di-icon-add-folder',
        text :'Folder',
        click :() => {
          this.showCreateDirectoryModal(parentId);
        }
      },
      {
        icon :'di-icon-dashboard',
        text :'Dashboard',
        click :() => {
          this.showCreateDashboardModal(parentId);
        }
      }
    ];
  }

  @Track(TrackEvents.DirectoryStar, {
    directory_id :(_: DirectoryListing, args: any) => args[0].id,
    directory_type :(_: DirectoryListing, args: any) => args[0].directoryType,
    directory_name :(_: DirectoryListing, args: any) => args[0].name
  })
  protected async star(directory: Directory) {
    try {
      await DirectoryModule.star(directory.id);
    } catch (ex) {
      Log.error('star::error', ex);
    }
  }

  @Track(TrackEvents.DirectoryRemoveStar, {
    directory_id :(_: DirectoryListing, args: any) => args[0].id,
    directory_type :(_: DirectoryListing, args: any) => args[0].directoryType,
    directory_name :(_: DirectoryListing, args: any) => args[0].name
  })
  protected async removeStar(directory: Directory) {
    try {
      await DirectoryModule.removeStar(directory.id);
    } catch (ex) {
      Log.error('removeStar::error', ex);
    }
  }

  @Track(TrackEvents.DirectoryMoveToTrash, {
    directory_id :(_: DirectoryListing, args: any) => args[0].id,
    directory_type :(_: DirectoryListing, args: any) => args[0].directoryType,
    directory_name :(_: DirectoryListing, args: any) => args[0].name
  })
  protected confirmDeleteDirectory(directory: Directory) {
    this.diContextMenu.hide();
    Log.debug('DeleteDirectory::', directory);
    Modals.showConfirmationModal(`Are you sure to delete ${directory.directoryType} '${directory.name}' ?`, {
      onOk :() => this.softDelete(directory)
    });
  }

  protected async handleMoveToDirectory(parentId: DirectoryId, directoryId: DirectoryId) {
    try {
      Log.debug('DirectoryListing::handleMoveToDirectory::id::', parentId, directoryId);
      DirectoryModule.setStatus(Status.Updating);
      await DirectoryModule.moveDirectory({ id :directoryId, parentId :parentId });
      await this.myData?.handler.loadDirectoryListing(parentId, this.myData?.createPaginationRequest());
    } catch (e) {
      Log.error('DirectoryListing::handleMoveToDirectory::error::', e);
      PopupUtils.showError(e.message);
    } finally {
      DirectoryModule.setStatus(Status.Loaded);
    }
  }

  protected async copy(parentId: DirectoryId, directory: Directory) {
    switch (directory.directoryType) {
      case DirectoryType.Dashboard:
      case DirectoryType.Query: {
        await this.copyDashboardTo(parentId, directory);
        break;
      }
      default: {
        PopupUtils.showError('Copy directory is not supported');
        break;
      }
    }
  }

  protected async copyDashboardTo(toParentId: DirectoryId, currentDashboard: Directory) {
    this.passwordModal.requirePassword(currentDashboard, currentDashboard.owner?.username || currentDashboard.ownerId, async () => {
      try {
        DirectoryModule.setStatus(Status.Updating);
        await this.duplicateDashboard(currentDashboard, toParentId);
        await this.navigateToDirectory(toParentId, '');
        DirectoryModule.setStatus(Status.Loaded);
      } catch (ex) {
        PopupUtils.showError(ex.message);
        DirectoryModule.setStatus(Status.Loaded);
      }
    });
  }

  protected async reload() {
    try {
      DirectoryModule.setStatus(Status.Updating);
      await this.myData?.handler.loadDirectoryListing(this.myData?.currentDirectoryId, this.myData?.createPaginationRequest());
    } catch (e) {
      Log.error('DirectoryListing::handleMoveToDirectory::error::', e);
      PopupUtils.showError(e.message);
    } finally {
      DirectoryModule.setStatus(Status.Loaded);
    }
  }
}
</script>
