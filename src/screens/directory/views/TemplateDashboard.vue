<!--<template>-->
<!--  <LayoutContent>-->
<!--    <LayoutHeader :route="rootRoute" title="Template" icon="di-icon-template-dashboard">-->
<!--      <DiSearchInput ref="inputSearch" transparent autofocus class="ml-auto" placeholder="Search template..." v-model="keyword" />-->
<!--    </LayoutHeader>-->
<!--    <div class="template-dashboard">-->
<!--      <div class="template-dashboard&#45;&#45;title">Use a Template</div>-->
<!--      <div class="template-dashboard&#45;&#45;description">-->
<!--        RocketBI uses your event data to generate a number of out - of - the - box, fully customizable reports that make it easy for any anyone to get started.-->
<!--      </div>-->
<!--      <StatusWidget class="template-dashboard&#45;&#45;body di-scroll-bar" :status="status" :error="errorMsg" @retry="init()">-->
<!--        <div-->
<!--          class="template-dashboard&#45;&#45;content"-->
<!--          :class="{-->
<!--            'template-dashboard&#45;&#45;content&#45;&#45;empty': isTemplateEmpty(templates)-->
<!--          }"-->
<!--        >-->
<!--          <template v-if="isTemplateEmpty(templates)">-->
<!--            <MyDataEmpty class="h-100" title="Template listing is empty"></MyDataEmpty>-->
<!--          </template>-->
<!--          <template v-for="(template, index) in templates">-->
<!--            <div :key="`${template.id}_${index}`" class="template-dashboard-card" @click="handleClickTemplate(template)">-->
<!--              <div class="template-dashboard-card&#45;&#45;header">-->
<!--                <DiImage class="template-dashboard-card&#45;&#45;header&#45;&#45;thumbnail" :src="template.thumbnail" />-->
<!--              </div>-->
<!--              <div-->
<!--                class="template-dashboard-card&#45;&#45;body"-->
<!--                :class="{-->
<!--                  'template-dashboard-card&#45;&#45;body&#45;&#45;two-line': template.name.length > 27-->
<!--                }"-->
<!--              >-->
<!--                <div class="template-dashboard-card&#45;&#45;body&#45;&#45;title" :title="template.name">{{ template.name }}</div>-->
<!--                <div class="template-dashboard-card&#45;&#45;body&#45;&#45;description" :title="template.description">{{ template.description }}</div>-->
<!--              </div>-->
<!--            </div>-->
<!--          </template>-->
<!--        </div>-->
<!--      </StatusWidget>-->
<!--    </div>-->
<!--    <DiRenameModal ref="createDashboardModal" title="Create Dashboard" label="Dashboard name" placeholder="Type dashboard name" action-name="Create" />-->
<!--  </LayoutContent>-->
<!--</template>-->

<!--<script lang="ts">-->
<!--import MyDataEmpty from '@/screens/dashboard-detail/components/MyDataEmpty.vue';-->
<!--import BreadcrumbComponent from '@/screens/directory/components/BreadcrumbComponent.vue';-->
<!--import { Routers, Status } from '@/shared';-->
<!--import DiTable from '@/shared/components/common/di-table/DiTable.vue';-->
<!--import DiRenameModal from '@/shared/components/DiRenameModal.vue';-->
<!--import DiSearchInput from '@/shared/components/DiSearchInput.vue';-->
<!--import { LayoutContent, LayoutHeader } from '@/shared/components/layout-wrapper';-->
<!--import { ListUtils, RouterUtils } from '@/utils';-->
<!--import { DashboardId, DIException, TemplateDashboardResponse } from '@core/common/domain';-->
<!--import { DashboardService } from '@core/common/services';-->
<!--import { Inject } from 'typescript-ioc';-->
<!--import { Component, Ref, Vue } from 'vue-property-decorator';-->
<!--import { DefaultDirectoryId } from './mydata/DefaultDirectoryId';-->

<!--@Component({-->
<!--  components: { DiRenameModal, DiSearchInput, LayoutHeader, LayoutContent, MyDataEmpty, DiTable, BreadcrumbComponent }-->
<!--})-->
<!--export default class TemplateDashboard extends Vue {-->
<!--  protected keyword = '';-->

<!--  protected templates: TemplateDashboardResponse[] = [];-->
<!--  protected status: Status = Status.Loading;-->
<!--  protected errorMsg = '';-->
<!--  protected LOAD_MORE_LIMIT = 20;-->

<!--  @Ref()-->
<!--  protected readonly createDashboardModal!: DiRenameModal;-->

<!--  @Inject-->
<!--  protected readonly dashboardService!: DashboardService;-->

<!--  mounted() {-->
<!--    this.init();-->
<!--  }-->

<!--  protected async init(): Promise<void> {-->
<!--    try {-->
<!--      this.errorMsg = '';-->
<!--      this.status = Status.Loading;-->
<!--      this.templates = await this.loadTemplates();-->
<!--      this.status = Status.Loaded;-->
<!--    } catch (error) {-->
<!--      this.status = Status.Error;-->
<!--      const ex = DIException.fromObject(error);-->
<!--      this.errorMsg = ex.getPrettyMessage();-->
<!--    }-->
<!--  }-->

<!--  protected async loadTemplates(): Promise<TemplateDashboardResponse[]> {-->
<!--    const result = await this.dashboardService.listTemplates(this.keyword, this.templates.length, this.LOAD_MORE_LIMIT);-->
<!--    return result.data;-->
<!--  }-->

<!--  protected get rootRoute() {-->
<!--    return {-->
<!--      name: Routers.TemplateDashboard,-->
<!--      query: {-->
<!--        token: RouterUtils.getToken(this.$route)-->
<!--      }-->
<!--    };-->
<!--  }-->

<!--  protected handleClickTemplate(template: TemplateDashboardResponse): void {-->
<!--    this.createDashboardModal.show(template.name, async (name: string) => {-->
<!--      try {-->
<!--        this.createDashboardModal.setLoading(true);-->
<!--        this.createDashboardModal.setError('');-->
<!--        const dashboardId = await this.createDashboard(name, template.id);-->
<!--        this.createDashboardModal.hide();-->
<!--        this.$nextTick(async () => {-->
<!--          await RouterUtils.to(Routers.Dashboard, {-->
<!--            params: {-->
<!--              name: RouterUtils.buildParamPath(dashboardId, name)-->
<!--            },-->
<!--            query: {-->
<!--              token: RouterUtils.getToken(this.$route)-->
<!--            }-->
<!--          });-->
<!--        });-->
<!--      } catch (ex) {-->
<!--        this.createDashboardModal.setLoading(false);-->
<!--        this.createDashboardModal.setError(ex.getPrettyMessage());-->
<!--      }-->
<!--    });-->
<!--  }-->

<!--  protected async createDashboard(name: string, templateId: number): Promise<DashboardId> {-->
<!--    const dashboard = await this.dashboardService.createDashboardFromTemplate(templateId, name, DefaultDirectoryId.MyData);-->
<!--    return dashboard.id;-->
<!--  }-->

<!--  protected isTemplateEmpty(templates: TemplateDashboard[]): boolean {-->
<!--    return ListUtils.isEmpty(templates);-->
<!--  }-->
<!--}-->
<!--</script>-->

<!--<style lang="scss">-->
<!--.template-dashboard {-->
<!--  background: white;-->
<!--  height: 100%;-->
<!--  border-radius: 4px;-->

<!--  padding: 30px;-->
<!--  text-align: left;-->
<!--  display: flex;-->
<!--  flex-direction: column;-->
<!--  overflow: hidden;-->

<!--  &&#45;&#45;title {-->
<!--    font-size: 18px;-->
<!--    font-weight: 500;-->
<!--    line-height: 1.4;-->
<!--    color: var(&#45;&#45;text-color);-->
<!--  }-->

<!--  &&#45;&#45;description {-->
<!--    font-size: 14px;-->
<!--    font-weight: 400;-->
<!--    line-height: 1.4;-->
<!--    color: #8e8e93;-->
<!--    margin-top: 10px;-->
<!--  }-->

<!--  &&#45;&#45;body {-->
<!--    margin-top: 24px;-->
<!--    overflow: auto;-->

<!--    .template-dashboard&#45;&#45;content {-->
<!--      flex: 1;-->
<!--      display: flex;-->
<!--      flex-direction: row;-->
<!--      flex-wrap: wrap;-->
<!--      grid-gap: 20px;-->
<!--      justify-content: center;-->

<!--      &.template-dashboard&#45;&#45;content&#45;&#45;empty {-->
<!--        height: 100%;-->
<!--      }-->

<!--      .template-dashboard-card {-->
<!--        background: white;-->
<!--        height: 327px;-->
<!--        width: 268px;-->
<!--        border-radius: 12px;-->
<!--        box-shadow: 0px 2px 4px 0px #0000001a;-->
<!--        border: 1px solid #e5e5ea;-->
<!--        cursor: pointer;-->

<!--        &:hover {-->
<!--          box-shadow: 0px 2px 4px 0px #0000001a;-->
<!--          border: 1px solid var(&#45;&#45;accent);-->
<!--        }-->

<!--        &&#45;&#45;header {-->
<!--          width: 100%;-->
<!--          padding: 12px;-->
<!--          height: 196px;-->
<!--          background: linear-gradient(180deg, #cbf2ff 0%, #b3c4ff 100%);-->
<!--          border-radius: 12px 12px 0 0;-->
<!--          overflow: hidden;-->

<!--          &&#45;&#45;thumbnail {-->
<!--            border: 1px solid #f0f0f0;-->
<!--            border-radius: 8px;-->
<!--          }-->
<!--        }-->

<!--        &&#45;&#45;body {-->
<!--          padding: 16px;-->

<!--          &&#45;&#45;title {-->
<!--            color: var(&#45;&#45;text-color);-->
<!--            font-weight: 700;-->
<!--            font-size: 16px;-->
<!--            line-height: 18.75px;-->
<!--            display: -webkit-box;-->
<!--            -webkit-line-clamp: 1;-->
<!--            -webkit-box-orient: vertical;-->
<!--            overflow: hidden;-->
<!--            text-overflow: ellipsis;-->
<!--          }-->

<!--          &&#45;&#45;description {-->
<!--            font-weight: 400;-->
<!--            font-size: 14px;-->
<!--            line-height: 1.4;-->
<!--            display: -webkit-box;-->
<!--            -webkit-line-clamp: 4;-->
<!--            -webkit-box-orient: vertical;-->
<!--            text-overflow: ellipsis;-->
<!--            overflow: hidden;-->
<!--            color: #808491;-->
<!--            margin-top: 8px;-->
<!--          }-->

<!--          &.template-dashboard-card&#45;&#45;body&#45;&#45;two-line {-->
<!--            .template-dashboard-card&#45;&#45;body&#45;&#45;title {-->
<!--              -webkit-line-clamp: 2;-->
<!--            }-->

<!--            .template-dashboard-card&#45;&#45;body&#45;&#45;description {-->
<!--              -webkit-line-clamp: 3;-->
<!--            }-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</style>-->
