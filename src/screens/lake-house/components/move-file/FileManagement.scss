@import '~@/themes/scss/mixin.scss';

.move-file {
  i {
    color: var(--text-color);
  }

  i.fa-spinner {
    color: var(--primary);
  }

  .ml-parent {
    margin-left: 3px;
  }

  .directory-name {
    @include regular-text-14();
    font-weight: 500;
    cursor: pointer;
  }

  &__header {
    .back {
      height: 44px;
      padding-left: 12px;
      width: 100%;

      i {
        font-size: 15.5px;
        margin-right: 12px;
      }

      &:hover {
        background-color: var(--active-color);
      }
    }
  }

  &__header-create {
    display: flex;
    align-items: center;
    height: 44px;
    padding: 16px 16px 0 8px;

    .icon-back {
      margin-right: 4px;
      font-size: 16px;
      padding: 2px;
    }

    input {
      height: 34px;
      padding-left: 16px;
    }

    .create-folder {
      padding: 9px;

      .title {
        display: none;
      }

      i {
        font-size: 16px;
        color: var(--secondary);
      }
    }
  }

  &__body {
    .file-listing {
      .file-item {
        height: 46px;
        display: flex;
        align-items: center;
        padding: 0 12px 0 16px;

        i {
          font-size: 15.5px;
        }

        &:hover {
          background-color: var(--active-color);
        }
        &.di-active {
          background-color: var(--active-color);
        }
      }
    }
  }

  &__body-create {
    position: relative;

    .error-message {
      position: absolute;
      top: 8px;
      left: 32px;
      width: calc(100% - 48px);
      background: var(--secondary);
      word-break: break-all;
    }

    > div {
      @include regular-text-14();
    }
  }

  &__footer {
    background: #fafafb;
    padding: 9px 15px 8px;
    height: 45px;
    display: flex;
    align-items: center;

    .di-button {
      height: 28px;
    }

    .icon-new-folder {
      border-radius: 50%;
      height: 28px;
      width: 28px;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 1.2;

      img {
      }

      &:hover {
        background-color: var(--icon-hover-color, #d6d6d6) !important;
      }
    }
  }
}
