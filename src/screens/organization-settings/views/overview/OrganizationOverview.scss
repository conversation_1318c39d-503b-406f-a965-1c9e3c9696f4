@import '~@/themes/scss/mixin.scss';

.org-settings-content-body {
  background: var(--secondary);
  padding: 24px;
  border-radius: 4px;
  flex: 1;

  > h6 {
    @include medium-text();
    text-align: justify;
    margin-bottom: 16px;
  }

  .org-overview {
    background-color: var(--secondary-2);
    display: flex;
    flex-direction: column;
    padding: 0 24px;
    border-radius: 12px;

    &-item {
      display: flex;
      border-bottom: 1px solid #f0f0f0;
      padding: 15px 0;
      align-items: center;
      text-align: left;
      min-height: 90px;

      .org-logo {
        width: 60px;
        height: 60px;
      }

      &:last-child {
        border-bottom: none;
      }

      &-label {
        margin-right: 10px;
        width: 140px;
        @include medium-text();
      }

      &-content {
        flex: 1;
        @include medium-text();
      }

      &-delete-content {
        flex: 1;
        @include regular-text(0.2px, var(--secondary-text-color));
      }

      &-action {
        width: 28px;
        height: 28px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        > i {
          font-size: 16px;
        }
      }
    }
  }
}
