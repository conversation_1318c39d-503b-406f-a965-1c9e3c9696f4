/*!
 * @author: tvc12 - Thien Vi
 * @created: 9/6/21, 4:23 PM
 */

@import '~@/themes/scss/mixin.scss';
@import '~@/themes/scss/di-variables';

.plan-and-billing-layout-content {
  overflow: auto !important;
  max-height: unset !important;
}

.plan-and-billing-content {
  background: var(--secondary);
  padding: 24px;
  border-radius: 4px;
  flex: 1;
  //overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  .org-settings-content-header {
    display: flex;
    justify-content: space-between;
    text-align: left;
    min-height: 56px;
    &-title {
      @include medium-text();
      text-transform: uppercase;
    }

    &-desc {
      @include regular-text();
      text-transform: none;
      margin-top: 12px;
    }
  }

  .date-countdown {
    display: flex;
    color: var(--secondary-text-color);
    &-item {
      display: flex;
      flex-direction: column;
      width: 50px;
      height: 52px;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      background-color: var(--primary);
      line-height: 1;
      margin: 2px;

      &:last-child {
        margin-right: 0;
      }

      &-value {
        text-align: center;
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 4px;
      }
      &-label {
        color: #5f6368;
        text-align: center;
        font-size: 9px;
        font-weight: normal;
      }
    }

    &.expired {
      color: #ff5630;
    }
  }

  .org-settings-content-body {
    flex: 1;
    //overflow: hidden;
    padding: 0;

    .plan-and-billing {
      $padding-x: 12px;
      display: flex;
      flex-direction: column;
      text-align: left;
      width: 100%;
      max-height: 100%;
      //overflow: hidden;

      &-header {
        display: flex;
        justify-content: space-between;
        padding: 24px 0 16px;
        align-items: center;
        line-height: 1;

        &-title {
          @include medium-text(24px);
          text-transform: uppercase;
        }

        &-actions {
          @include medium-text(16px);
        }
      }

      &-body {
        width: calc(100% + #{$padding-x * 2});
        margin: 0 -$padding-x;
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        //overflow: auto;

        @media screen and (max-width: 1000px) {
          justify-content: center;
        }
      }
    }
  }
}

@include media-breakpoint-down(sm) {
  .plan-and-billing-content {
    padding: 16px;
    .org-settings-content-body {
      .plan-and-billing-header {
        flex-wrap: wrap;
        margin-bottom: 0;
        padding: 16px 0 16px;
        .plan-and-billing-header-actions {
          margin-top: 4px;
        }
      }
      .plan-and-billing-header-title {
        font-size: 18px;
      }
      .plan-type-item {
        width: calc(100% - 24px);
        margin: 4px 12px;
        padding: 20px;
      }
      .plan-type-item-header {
        flex-wrap: wrap;
        .plan-type-item-header-price {
          font-size: 20px;
          margin-left: auto;
        }
      }
      .plan-and-billing-body {
        margin-bottom: 20px;
      }
    }
  }
}
