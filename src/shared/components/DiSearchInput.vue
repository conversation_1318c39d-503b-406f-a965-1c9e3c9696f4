<template>
  <div class="di-search--input" :class="{ 'di-search--input--transparent': transparent }" :border="border">
    <slot>
      <SearchIcon></SearchIcon>
    </slot>
    <BInput
      ref="input"
      :value="value"
      :debounce="debounce"
      :autofocus="autofocus"
      autocomplete="off"
      v-bind="$attrs"
      @blur="handleUnFocus"
      @input="value => handleSubmitValue(value)"
      @keydown.enter="handleEnter"
    />
    <div v-if="isNotEmpty" @click.stop="clearText" class="di-search--clear-text">
      <i class="di-icon-close btn-icon-border"></i>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Model, Prop, Ref, Vue } from 'vue-property-decorator';
import { TimeoutUtils } from '@/utils';
import { StringUtils } from '@/utils/StringUtils';
import { BFormInput } from 'bootstrap-vue';

@Component({
  inheritAttrs: true
})
export default class DiSearchInput extends Vue {
  protected processIdSubmitChange: null | number = null;
  @Model('input', { required: true, type: String, default: '' })
  protected readonly value!: string;

  @Prop({ required: false, type: Boolean, default: false })
  protected readonly border!: boolean;

  @Prop({ required: false, default: 300 })
  protected readonly debounce!: number;

  @Prop({ required: false, default: false })
  protected readonly autofocus!: boolean;

  @Prop({ required: false, type: Boolean, default: false })
  protected readonly transparent!: boolean;

  protected curValue = '';

  @Ref()
  protected readonly input!: BFormInput;

  protected get isNotEmpty() {
    return StringUtils.isNotEmpty(this.value);
  }

  protected handleEnter(): void {
    if (this.processIdSubmitChange) {
      clearTimeout(this.processIdSubmitChange);
      this.processIdSubmitChange = null;
    }
    this.$emit('change', this.curValue);
    this.$emit('enter');
  }

  protected handleUnFocus(): void {
    this.$emit('blur');
  }

  protected handleSubmitValue(text: string): void {
    this.curValue = text;
    this.$emit('input', text);

    this.processIdSubmitChange = TimeoutUtils.waitAndExec(
      this.processIdSubmitChange,
      () => {
        this.$emit('change', this.curValue);
      },
      this.debounce
    );
  }

  protected clearText(): void {
    if (this.processIdSubmitChange) {
      clearTimeout(this.processIdSubmitChange);
      this.processIdSubmitChange = null;
    }
    this.curValue = '';
    this.$emit('input', '');
    this.$emit('change', '');
    this.$emit('clear');

    this.focus();
  }

  focus() {
    this.input?.focus();
  }
}
</script>

<style lang="scss">
.di-search--input {
  display: flex;
  flex-direction: row;
  align-items: center;
  background: white;
  height: 34px;
  border-radius: 4px;

  &.di-search--input--transparent {
    background: transparent;
  }

  &[border] {
    border: 1px solid rgba(231, 235, 238, 1);
  }

  > svg {
    flex-shrink: 1;
    margin: 12px 8px 12px 14px;
  }

  input.form-control {
    flex: 1;
    background: transparent;
    height: unset;
    margin-right: 14px;

    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: var(--text-color);

    &::placeholder {
      color: var(--secondary-text-color);
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
    }
  }

  .di-search--clear-text {
    margin-right: 8px;
  }
}
</style>
