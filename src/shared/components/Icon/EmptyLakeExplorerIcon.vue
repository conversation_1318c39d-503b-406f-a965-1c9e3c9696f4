<template>
  <svg width="41" height="40" viewBox="0 0 41 40" xmlns="http://www.w3.org/2000/svg">
    <g fill="none" fill-rule="evenodd">
      <path d="M.5 0h40v40H.5z" />
      <path
        d="M7.793 31.707a1 1 0 0 1 1 1v5.805a1 1 0 0 1-1 1H1.988a1 1 0 0 1-1-1v-5.805a1 1 0 0 1 1-1h5.805zm-.501 1.5H2.488v4.805h4.804v-4.805zm31.72 1.653a.75.75 0 0 1 .102 1.493l-.102.007H15.598a.75.75 0 0 1-.102-1.493l.102-.007h23.414zM7.792 16.098a1 1 0 0 1 1 1v5.804a1 1 0 0 1-1 1H1.989a1 1 0 0 1-1-1v-5.804a1 1 0 0 1 1-1h5.805zm-.5 1.499H2.488v4.805h4.804v-4.805zm31.72 1.653a.75.75 0 0 1 .102 1.493l-.102.007H15.598a.75.75 0 0 1-.102-1.493l.102-.007h23.414zM7.792.488a1 1 0 0 1 1 1v5.805a1 1 0 0 1-1 1H1.989a1 1 0 0 1-1-1V1.488a1 1 0 0 1 1-1h5.805zm-.5 1.5H2.488v4.804h4.804V1.988zm31.72 1.652a.75.75 0 0 1 .102 1.493l-.102.007H15.598a.75.75 0 0 1-.102-1.493l.102-.007h23.414z"
        fill="#5F6368"
        fill-rule="nonzero"
      />
    </g>
  </svg>
</template>

<script lang="ts">
import Component from 'vue-class-component';
import Vue from 'vue';

@Component
export default class EmptyLakeExplorerIcon extends Vue {}
</script>
