.sl-vue-tree {
  position: relative;
  cursor: default;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

.sl-vue-tree.sl-vue-tree-root {
  color: var(--text-color);
}

.sl-vue-tree-root > .sl-vue-tree-nodes-list {
  overflow: hidden;
  position: relative;
  padding-bottom: 4px;
}

.sl-vue-tree-selected > .sl-vue-tree-node-item {
  color: var(--text-color);
}

.sl-vue-tree-node-item:hover,
.sl-vue-tree-node-item.sl-vue-tree-cursor-hover {
  color: var(--text-color);
  border: 0 none;
}

.sl-vue-tree-node-item {
  position: relative;
  display: flex;
  flex-direction: row;
  line-height: 3;
  border: 0 none;
}

.sl-vue-tree-node-item.sl-vue-tree-cursor-inside {
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.sl-vue-tree-gap {
  width: 28px;
  /*width: 25px;*/
  /*min-height: 1px;*/
}

.sl-vue-tree-node-is-leaf > .sl-vue-tree-title > div {
  align-items: center;
  display: flex;
  flex-direction: row;
}

.sl-vue-tree-node-is-leaf > .sl-vue-tree-title > div > span {
  flex: 1;
  margin-right: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sl-vue-tree-toggle {
  display: block;
  text-align: left;
  font-size: 16px;
  width: 16px;
}

.sl-vue-tree-node {
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.2px;
  color: var(--text-color);
}
.sl-vue-tree-title {
  cursor: pointer;
  text-align: left;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
}
.sl-vue-tree-node-is-folder .sl-vue-tree-title {
  font-weight: 500;
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.17px;
  color: var(--text-color);
  width: 100%;
  font-size: 12px;
  padding: 5px 1px 2px 5px;
}

.sl-vue-tree-node-is-folder .sl-vue-tree-title > div {
  display: flex;
  align-items: center;
  justify-content: left;
}

.sl-vue-tree-node-is-folder .sl-vue-tree-title > div > .sl-vue-tree-toggle-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sl-vue-tree-title svg {
  width: 16px;
  margin-right: 10px;
  margin-bottom: 2px;
}

.sl-vue-tree-title img.chart-control-icon {
  width: 16px;
  margin-right: 10px;
}

.sl-vue-tree-sidebar {
  margin-left: auto;
}

.sl-vue-tree-cursor {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.5);
  height: 1px;
  width: 100%;
}

.sl-vue-tree-drag-info {
  position: absolute;
  opacity: 0.5;
  margin-left: 20px;
  padding: 5px 10px;
}
