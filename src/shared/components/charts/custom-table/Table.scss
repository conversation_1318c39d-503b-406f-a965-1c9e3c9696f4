/*
 * @author: tvc12 - Thien Vi
 * @created: 5/4/21, 6:00 PM
 */

@import '~@/themes/scss/mixin.scss';
@import 'src/shared/components/charts/custom-table/DefaultTableStyle';

.custom-table {
  border-radius: 4px;
}

.infinite-table {
  //overflow: auto;
  border-radius: 4px;
  font-size: 13px;
  display: flex;
  flex: auto;
  align-items: flex-start;

  > .table-boundary {
    position: relative;

    .fake-scroll-area {
      position: absolute;
    }
  }

  @mixin cell() {
    min-width: var(--column-width);
    max-width: var(--max-column-width);

    padding-left: 12px;
    padding-right: 12px;
    text-overflow: ellipsis;
    overflow: hidden;

    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.2px;
  }

  tbody tr {
    &.expanded td {
      font-weight: bold;
    }

    @mixin row-common-style() {
      font-size: var(--row-font-size, $default-row-font-size);
      font-family: var(--row-font-family, $default-row-font-family), sans-serif;
      white-space: var(--row-white-space, $default-row-white-space);
      text-align: $default-row-text-align;
      font-stretch: normal;
      font-style: normal;
      line-height: normal;
      letter-spacing: 0.23px;
    }

    &.even > * {
      background-color: var(--row-even-background-color, $default-row-even-background-color);
      color: var(--row-even-color, $default-row-even-color);
      @include row-common-style();
    }

    &.odd > * {
      background-color: var(--row-odd-background-color, $default-row-odd-background-color);
      color: var(--row-odd-color, $default-row-odd-color);
      @include row-common-style();
    }
    .text-align-right {
      text-align: right;
    }
  }

  /**
 style icon collapsed
 */

  td.expandable {
    display: flex;
    align-items: center;

    > div.collapse-icon {
      float: left;
      //background: var(--icon-background-color, #FFFFFF19);
      margin-right: 5px;

      text-align: center;
      font-size: 18px;
      line-height: 16px;
      font-weight: bold;
      box-sizing: border-box;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    > div.icon-bar {
      flex: 1;

      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  tfoot td {
    @include cell();

    position: -webkit-sticky !important;
    position: sticky !important;
    bottom: 0;
    @include bold-text();
    z-index: 4;

    &.pinned {
      z-index: 5;
    }

    height: var(--footer-cell-height, 48px);
    background: var(--footer-background-color, $default-footer-background-color);
    color: var(--footer-color, $default-footer-text-color);
    text-align: var(--footer-text-align, $default-footer-text-align);
    font-size: var(--footer-font-size, $default-footer-font-size);
    white-space: var(--footer-white-space, $default-footer-white-space);
    font-family: var(--footer-font-family, $default-footer-font-family), sans-serif;
  }

  .pinned {
    position: sticky !important;
    text-align: left !important;
  }

  th.pinned {
    z-index: 3 !important;
  }

  tr td.pinned {
    z-index: 2 !important;
  }

  table {
    min-width: 100%;
  }

  table > div.scroller {
    display: table-row;
  }

  table > * {
    white-space: nowrap;
  }

  table td {
    vertical-align: middle;
  }

  tbody tr td {
    @include cell();

    height: var(--body-cell-height, 48px);
    z-index: 0;

    &.data-bar {
      position: relative;

      > span {
        position: relative;
      }

      > div.data-bar-panel {
        position: absolute;
        top: 1px;
        bottom: 1px;
        left: 12px;
        right: 12px;
        > div {
          position: absolute;
          top: 8px;
          bottom: 8px;
          //height: 100%;
        }
      }
    }
  }

  thead tr th {
    @include cell();
    height: var(--header-cell-height, 48px);
    color: var(--table-header-color, $default-header-color);
    background-color: var(--header-background-color, $default-header-background-color);
    font-family: var(--header-font-family, $default-header-font-family), sans-serif;
    font-size: var(--header-font-size, $default-header-font-size);
    white-space: var(--header-white-space, $default-header-white-space);
    text-align: var(--header-text-align, $default-header-text-align);
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: 0.2px;

    position: sticky;
    z-index: 1 !important;

    font-weight: bold;

    //&[colspan]:not([colspan='1']) {
    //  text-align: center;
    //}

    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+/Edge */
    user-select: none; /* Standard */

    //&:not(:last-child){
    .resizer {
      position: absolute;

      z-index: 5;
      top: 0;
      right: 0;
      width: 8px;
      height: 100%;
      cursor: col-resize;

      //&:hover{
      //  border-right: 2px solid var(--primary);
      //}

      //&::after{
      //  margin-right: -4px;
      //  display: flex;
      //  align-items: center;
      //  height: 100%;
      //  font-family: data-insider-icon !important;
      //  content: "\e973";
      //  font-size: 20px;
      //  color: var(--text-color);
      //  opacity: 0.2;
      //  z-index: 1005;
      //}
    }
    //}
  }

  .sort-icon {
    border-radius: 0;
    display: flex;
    justify-content: center;
  }

  .btn-ghost {
    &:hover {
      img,
      svg {
        filter: brightness(125%);
      }
    }
  }

  tbody tr td,
  tfoot td {
    > div.icon-bar {
      display: flex;
      align-items: center;
      height: 100%;

      > span {
        z-index: 1;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      > div.icon {
        z-index: 1;
        line-height: normal;
        font-size: inherit;

        > img {
          line-height: normal;
          width: var(--row-font-size, $default-row-font-size);
          height: var(--row-font-size, $default-row-font-size);
          //font-size: inherit;
        }
      }

      &.icon-layout-left {
        > span {
          order: 1;
        }

        > div.icon {
          order: 0;
          margin-left: 0;
          margin-right: 5px;
        }
      }

      &.icon-layout-right {
        > span {
          flex: 1;
          order: 0;
        }

        > div.icon {
          order: 1;
          margin-left: 5px;
          margin-right: 0;
        }
      }

      &.icon-layout-only-icon {
        > span {
          display: none;
        }

        > div.icon {
          flex: 1;
          text-align: inherit;
        }
      }

      &.icon-align-top > div.icon {
        align-self: flex-start;
      }
      &.icon-align-center > div.icon {
        align-self: center;
      }
      &.icon-align-bottom > div.icon {
        align-self: flex-end;
      }
    }
  }
}

.custom-scroll-bar {
  &::-webkit-scrollbar {
    display: unset; /* Chrome, Safari and Opera */
    width: 5px;
    height: 5px;
  }

  //the buttons on the scrollbar (arrows pointing upwards and downwards).
  &::-webkit-scrollbar-button {
    display: none;
  }

  // he draggable scrolling handle.
  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-background);
    border-radius: 4px;

    &:hover {
      background: var(--scrollbar-hover);
    }
  }

  // process bar
  //the track (progress bar) of the scrollbar.
  &::-webkit-scrollbar-track {
    //
  }

  // the track (progress bar) NOT covered by the handle.
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }

  //the bottom corner of the scrollbar, where both horizontal and vertical scrollbars meet.
  &::-webkit-scrollbar-corner {
    display: none;
  }

  //the draggable resizing handle that appears at the bottom corner of some elements.
  &::-webkit-resizer {
    display: none;
  }

  & {
    -ms-overflow-style: block; /* IE and Edge */
    scrollbar-width: 1 !important; /* Firefox */
  }
}

.table-right-border .infinite-table table {
  border-collapse: separate;
  border-spacing: 0;

  @mixin border-style {
    border-left-color: rgba(255, 255, 255, 0.1);
    border-left-width: 1px;
    border-left-style: solid;
  }

  thead {
    th + th {
      @include border-style();
    }
  }

  tbody {
    td + td {
      @include border-style();
    }
  }
}

div.table-grid.custom-table .infinite-table table {
  border-collapse: separate;
  border-spacing: 0;

  thead {
    $border-vertical-style: var(--grid-header-vertical-style);
    $border-horizontal-style: var(--grid-header-horizontal-style);

    tr:not(:first-child) {
      > th {
        border-top: $border-horizontal-style;
      }
    }

    th[col-index='0'] {
      &:last-child {
        border-right: none;
      }
    }

    th[col-index='1'] {
      border-left: $border-vertical-style;
    }

    th + th {
      border-left: $border-vertical-style;
    }
  }

  tbody {
    $border-vertical-style: var(--grid-body-vertical-style);
    $border-horizontal-style: var(--grid-body-horizontal-style);

    td {
      padding-top: var(--grid-horizontal-padding);
      padding-bottom: var(--grid-horizontal-padding);
    }

    td + td {
      border-left: $border-vertical-style;
    }

    tr td {
      border-bottom: $border-horizontal-style;
    }
  }

  tfoot {
    $border-vertical-style: var(--grid-footer-vertical-style);
    $border-horizontal-style: var(--grid-footer-horizontal-style);

    td {
      padding-top: var(--grid-horizontal-padding);
      padding-bottom: var(--grid-horizontal-padding);
    }

    td + td {
      border-left: $border-vertical-style;
    }

    tr td {
      border-bottom: $border-horizontal-style;
    }
  }

  &.hide-footer tbody tr:last-child td {
    border-bottom: none;
  }
}

.custom-table-tooltip {
  display: -webkit-box;
  position: absolute;
  z-index: 9999;
  padding: 6px 16px;
  max-width: 30vw;
  max-height: 320px;
  overflow: hidden;
  text-overflow: ellipsis;
  //word-break: normal;
  -webkit-line-clamp: 15;
  -webkit-box-orient: vertical;

  color: var(--tooltip-color, $default-tooltip-color);
  background-color: var(--tooltip-background-color, $default-tooltip-background-color);
  font-family: var(--tooltip-font-family, $default-tooltip-font-family), sans-serif;
  font-size: var(--tooltip-font-size, $default-tooltip-font-size);
  border-radius: 1px;
}
