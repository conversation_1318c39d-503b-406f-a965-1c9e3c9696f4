.input-filter-container {
  display: flex;
  flex-direction: row;
  align-items: center;

  .filter-chart {
    margin-right: 8px;
  }
  .input-filter-title {
    max-width: 50%;
  }

  .input-setting {
    flex: 1;
  }
}

.input-filter {
  .btn-picker {
    background-color: transparent;
    border: solid #d6d6d6 1px !important;
    .title {
      padding: 0 4px;
    }
  }
  .input-setting {
    .form-control {
      background-color: transparent;
      border: solid #d6d6d6 1px !important;
      padding: 0 4px !important;
      height: 32px !important;
    }

    .form-input {
      border: solid transparent 0 !important;
    }
  }
}
