$bg-color: var(--accent);
$height: 6px;
.irs {
  font-family: Montserrat, sans-serif;
}

.range-slider-container {
  width: 100%;

  .irs--round {
    height: $height;
    box-shadow: none;
    top: 1px;
    .irs-handle {
      //no delete this
      top: 1px;
      z-index: 1 !important;
      height: 14px;
      width: 14px;
      border: 2px solid white;
      background-color: $bg-color;
      box-shadow: none;
    }
    .irs-line {
      background-color: #f2f2f7;
      top: 6px;
      height: $height;
    }

    .irs-from,
    .irs-to,
    .irs-single {
      background-color: $bg-color;
      font-size: 100%;
    }
    .irs-min,
    .irs-max,
    .irs-grid-text {
      font-size: 100%;
    }

    .irs-from::before,
    .irs-to::before,
    .irs-single::before {
      border-top-color: $bg-color;
    }

    .irs-bar {
      background-color: $bg-color;
      height: $height;
      top: 6px;
    }
  }
}
