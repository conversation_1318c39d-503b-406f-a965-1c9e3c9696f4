<template>
  <PanelHeader :header="tabTitle" target-id="y-axis-tab">
    <div class="y-axis-tab">
      <ToggleSetting id="y-axis-enable" :value="enabled" class="mb-3 group-config" label="On" @onChanged="handleAxisEnabled" />
      <div :style="axisSettingStyle">
        <template v-if="isShowPostfixPrefixConfig">
          <!--      Prefix setting-->
          <!--      Postfix setting-->
          <div class="row-config-container">
            <InputSetting
              id="y-axis-prefix-input"
              :value="prefixText"
              class="mr-2"
              label="Prefix"
              placeholder="Input Prefix"
              size="half"
              :maxLength="defaultSetting.prefixMaxLength"
              @onChanged="handlePrefixSaved"
            />
            <InputSetting
              id="y-axis-postfix-input"
              :value="postfixText"
              label="Postfix"
              placeholder="Input Postfix"
              size="half"
              @onChanged="handlePostfixSaved"
              :maxLength="defaultSetting.suffixMaxLength"
            />
          </div>
        </template>
        <DropdownSetting
          id="y-axis-category-font-family"
          :options="fontOptions"
          :value="categoryFont"
          class="mb-2"
          label="Font family"
          size="full"
          @onChanged="handleCategoryFontChanged"
        />
        <div class="row-config-container">
          <ColorSetting
            id="y-axis-category-font-color"
            :default-color="defaultSetting.categoryColor"
            :value="categoryColor"
            class="mr-2"
            size="small"
            @onChanged="handleCategoryColorChanged"
          />
          <DropdownSetting
            id="y-axis-category-font-size"
            :options="fontSizeOptions"
            :value="categoryFontSize"
            size="small"
            @onChanged="handleCategoryFontSizeChanged"
          />
        </div>
        <ToggleSetting id="y-axis-title-enable" :value="titleEnabled" class="mb-3 group-config" label="Axis title" @onChanged="handleTitleEnabled" />
        <InputSetting
          id="y-axis-title-input"
          placeholder="Input Axis Title"
          :style="titleSettingStyle"
          :value="title"
          class="mb-3"
          size="full"
          @onChanged="handleTitleSaved"
        />
        <DropdownSetting
          id="y-axis-title-font-family"
          :options="fontOptions"
          :style="titleSettingStyle"
          :value="titleFont"
          class="mb-2"
          label="Font family"
          size="full"
          @onChanged="handleTitleFontChanged"
        />
        <div :style="titleSettingStyle" class="row-config-container">
          <ColorSetting
            id="y-axis-title-font-color"
            :default-color="defaultSetting.titleColor"
            :value="titleColor"
            class="mr-2"
            size="small"
            @onChanged="handleTitleColorChanged"
          />
          <DropdownSetting id="y-axis-title-font-size" :options="fontSizeOptions" :value="titleFontSize" size="small" @onChanged="handleTitleFontSizeChanged" />
        </div>
        <div v-if="enableGridSetting">
          <ToggleSetting id="y-axis-grid-enable" :value="gridEnabled" class="mb-3 group-config" label="Gridlines" @onChanged="handleGridEnabled" />
          <div :style="gridLineChildrenSettingStyle" class="row-config-container">
            <ColorSetting
              id="y-axis-grid-line-color"
              :default-color="defaultSetting.gridLineColor"
              :value="gridLineColor"
              class="mr-2"
              size="half"
              @onChanged="handleGridColorChanged"
            />
            <InputSetting id="y-axis-grid-line-width" :value="gridLineWidth" type="number" size="small" @onChanged="handleGridLineWidthChanged" />
          </div>
          <DropdownSetting
            id="y-axis-grid-line-dash-style"
            :options="dashOptions"
            :style="gridLineChildrenSettingStyle"
            :value="gridLineDashStyle"
            size="full"
            @onChanged="handleGridLineDashStyleChanged"
          />
        </div>
        <!--    Config Min Max For YAxis-->
        <ToggleSetting
          v-if="isShowMinMaxConfig"
          id="config-min-max-for-y-axis"
          :value="enableMinMaxCondition"
          class="mb-2 group-config"
          :disable="!enabled"
          label="Config Min Max For Y-Axis"
          @onChanged="handleConditionChanged"
        />
        <div v-if="isShowMinMaxConfig" :style="minMaxConditionStyle" class="row-config-container">
          <div>
            <ToggleSetting
              id="min-condition"
              :disable="!enableMinMaxCondition || !enabled"
              :value="enableMinCondition"
              class="group-config mb-1"
              label="Min"
              @onChanged="handleMinConditionChanged"
            />
            <InputSetting
              applyFormatNumber
              :style="minConditionStyle"
              id="y-axis-min-condition"
              type="number"
              class="mr-2"
              label="Min Value"
              size="md"
              :value="minYAxis"
              @onChanged="handleMinValueChanged"
            ></InputSetting>
          </div>
          <div>
            <ToggleSetting
              id="max-condition"
              :disable="!enableMinMaxCondition || !enabled"
              :value="enableMaxCondition"
              class="group-config mb-1"
              label="Max"
              @onChanged="handleMaxConditionChanged"
            />
            <InputSetting
              applyFormatNumber
              :style="maxConditionStyle"
              id="y-axis-max-condition"
              type="number"
              label="Max Value"
              size="md"
              :value="maxYAxis"
              @onChanged="handleMaxValueChanged"
            ></InputSetting>
          </div>
        </div>
      </div>
      <RevertButton class="mb-3" style="text-align: right" @click="handleRevert" />
    </div>
  </PanelHeader>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { AxisSetting, ChartOption, HeatMapQuerySetting, QuerySetting, QuerySettingClassName, SeriesQuerySetting, SettingKey } from '@core/common/domain';
import PanelHeader from '@/screens/chart-builder/setting-modal/PanelHeader.vue';
import { FontFamilyOptions } from '@/shared/settings/common/options/FontFamilyOptions';
import { SecondaryFontSizeOptions } from '@/shared/settings/common/options/FontSizeOptions';
import { DashOptions } from '@/shared/settings/common/options/DashOptions';
import { enableCss } from '@/shared/settings/common/install';
import { Log } from '@core/utils';
import { ChartType } from '@/shared';
import { get } from 'lodash';

@Component({ components: { PanelHeader } })
export default class YAxisTab extends Vue {
  @Prop({ required: false, type: Array })
  private readonly setting!: AxisSetting[];
  @Prop({ required: false, type: Object })
  private readonly query!: QuerySetting;
  @Prop({ required: false, type: String })
  private readonly chartType?: ChartType;

  private readonly defaultSetting = {
    visible: true,
    categoryFont: ChartOption.getSecondaryFontFamily(),
    categoryColor: ChartOption.getPrimaryTextColor(),
    categoryFontSize: '11px',
    titleEnabled: true,
    titleFont: ChartOption.getSecondaryFontFamily(),
    titleColor: ChartOption.getPrimaryTextColor(),
    titleFontSize: '11px',
    gridLineColor: ChartOption.getGridLineColor(),
    gridLineDashStyle: 'Solid',
    gridLineWidth: '0.5',
    min: '0',
    max: '10000',
    prefixMaxLength: 10,
    suffixMaxLength: 10,
    prefixText: '',
    postfixText: ''
  };

  protected getDefaultTitle(): string {
    switch (this.query.className) {
      case QuerySettingClassName.Series:
        return get(this.query, 'yAxis[0].name', 'Untitled');
      case QuerySettingClassName.HeatMap:
        return get(this.query, 'yAxis.name', 'Untitled');
      default:
        return '';
    }
  }

  private get tabTitle(): string {
    if (this.chartType == ChartType.Bar || this.chartType == ChartType.StackedBar) {
      return 'X Axis';
    }
    return 'Y Axis';
  }

  private get isShowMinMaxConfig() {
    switch (this.chartType) {
      case ChartType.StackedColumn:
      case ChartType.StackedBar:
      case ChartType.StackedLine:
        return true;
      default:
        return false;
    }
  }

  private get isShowPostfixPrefixConfig() {
    switch (this.chartType) {
      default:
        return true;
    }
  }

  private get enabled(): boolean {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.visible ?? this.defaultSetting.visible;
    }
    return this.defaultSetting.visible;
  }

  private get categoryFont(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.labels?.style?.fontFamily ?? this.defaultSetting.categoryFont;
    }
    return this.defaultSetting.categoryFont;
  }

  private get categoryColor(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.labels?.style?.color ?? this.defaultSetting.categoryColor;
    }
    return this.defaultSetting.categoryColor;
  }

  private get categoryFontSize(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.labels?.style?.fontSize ?? this.defaultSetting.categoryFontSize;
    }
    return this.defaultSetting.categoryFontSize;
  }

  private get titleEnabled(): boolean {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.title?.enabled ?? this.defaultSetting.titleEnabled;
    }
    return this.defaultSetting.titleEnabled;
  }

  private get titleFont(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.title?.style?.fontFamily ?? this.defaultSetting.titleFont;
    }
    return this.defaultSetting.titleFont;
  }

  private get titleColor(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.title?.style?.color ?? this.defaultSetting.titleColor;
    }
    return this.defaultSetting.titleColor;
  }

  private get titleFontSize(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0]?.title?.style?.fontSize ?? this.defaultSetting.titleFontSize;
    }
    return this.defaultSetting.titleFontSize;
  }

  private get fontOptions() {
    return FontFamilyOptions;
  }

  private get fontSizeOptions() {
    return SecondaryFontSizeOptions;
  }

  private get title(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0].title?.text ?? this.getDefaultTitle();
    }
    return this.getDefaultTitle();
  }

  private get gridLineColor(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0].gridLineColor ?? this.defaultSetting.gridLineColor;
    }
    return this.defaultSetting.gridLineColor;
  }

  private get gridLineWidth(): string {
    if (this.setting && this.setting[0]) {
      return `${this.setting[0].gridLineWidth}` ?? this.defaultSetting.gridLineWidth;
    }
    return this.defaultSetting.gridLineWidth;
  }

  private get maxYAxis(): string {
    return get(this.setting, '[0].condition.max.value', this.defaultSetting.max);
  }

  private get minYAxis(): string {
    return get(this.setting, '[0].condition.min.value', this.defaultSetting.min);
  }

  private get gridLineDashStyle(): string {
    if (this.setting && this.setting[0]) {
      return this.setting[0].gridLineDashStyle ?? this.defaultSetting.gridLineDashStyle;
    }
    return this.defaultSetting.gridLineDashStyle;
  }

  private get dashOptions() {
    return DashOptions;
  }

  private get gridEnabled(): boolean {
    if (this.setting && this.setting[0]) {
      return this.setting[0].gridLineWidth != '0';
    }
    return false;
  }

  private get enableMinMaxCondition(): boolean {
    return this.setting[0]?.condition?.enabled ?? false;
  }

  private get enableMinCondition(): boolean {
    return get(this.setting, '[0].condition.min.enabled', false);
  }

  private get enableMaxCondition(): boolean {
    return get(this.setting, '[0].condition.max.enabled', false);
  }

  private get prefixText(): string {
    return this.setting[0]?.prefix?.text ?? this.defaultSetting.prefixText;
  }

  private get postfixText(): string {
    return this.setting[0]?.postfix?.text ?? this.defaultSetting.postfixText;
  }

  private get gridLineChildrenSettingStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.gridEnabled && this.enabled),
      marginBottom: '16px'
    } as CSSStyleDeclaration;
  }

  private get axisSettingStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.enabled)
    } as CSSStyleDeclaration;
  }

  private get titleSettingStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.enabled && this.titleEnabled)
    } as CSSStyleDeclaration;
  }

  private get minMaxConditionStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.enableMinMaxCondition)
    } as CSSStyleDeclaration;
  }

  private get minConditionStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.enableMinCondition)
    } as CSSStyleDeclaration;
  }

  private get maxConditionStyle(): CSSStyleDeclaration {
    return {
      ...enableCss(this.enableMaxCondition)
    } as CSSStyleDeclaration;
  }

  private get enableGridSetting(): boolean {
    switch (this.query.className) {
      case QuerySettingClassName.HeatMap:
        return false;
      default:
        return true;
    }
  }

  created() {
    Log.debug('created::', this.setting);
    if (!this.setting) {
      this.handleRevert();
    }
  }

  private handleGridEnabled(enabled: boolean) {
    if (enabled) {
      return this.$emit('onChanged', 'yAxis[0].gridLineWidth', '0.5');
    } else {
      return this.$emit('onChanged', 'yAxis[0].gridLineWidth', '0');
    }
  }

  private handleAxisEnabled(enabled: boolean) {
    return this.$emit('onChanged', 'yAxis[0].visible', enabled);
  }

  private handleCategoryFontChanged(newFont: string) {
    return this.$emit('onChanged', 'yAxis[0].labels.style.fontFamily', newFont);
  }

  private handleCategoryFontSizeChanged(newFontSize: string) {
    return this.$emit('onChanged', 'yAxis[0].labels.style.fontSize', newFontSize);
  }

  private handleCategoryColorChanged(newColor: string) {
    return this.$emit('onChanged', 'yAxis[0].labels.style.color', newColor);
  }

  private handleTitleEnabled(enabled: boolean) {
    return this.$emit('onChanged', 'yAxis[0].title.enabled', enabled);
  }

  private handleTitleSaved(newText: string) {
    return this.$emit('onChanged', 'yAxis[0].title.text', newText);
  }

  private handleTitleFontChanged(newFont: string) {
    return this.$emit('onChanged', 'yAxis[0].title.style.fontFamily', newFont);
  }

  private handleTitleColorChanged(newColor: string) {
    return this.$emit('onChanged', 'yAxis[0].title.style.color', newColor);
  }

  private handleTitleFontSizeChanged(newFontSize: string) {
    return this.$emit('onChanged', 'yAxis[0].title.style.fontSize', newFontSize);
  }

  private handleRevert() {
    const settingAsMap: Map<SettingKey, boolean | string | number> = new Map();
    settingAsMap.set('yAxis[0].visible', this.defaultSetting.visible);
    settingAsMap.set('yAxis[0].labels.style.fontFamily', this.defaultSetting.categoryFont);
    settingAsMap.set('yAxis[0].labels.style.fontSize', this.defaultSetting.categoryFontSize);
    settingAsMap.set('yAxis[0].labels.style.color', this.defaultSetting.categoryColor);
    settingAsMap.set('yAxis[0].title.enabled', this.defaultSetting.titleEnabled);
    settingAsMap.set('yAxis[0].title.text', this.getDefaultTitle());
    settingAsMap.set('yAxis[0].title.style.fontFamily', this.defaultSetting.titleFont);
    settingAsMap.set('yAxis[0].title.style.fontSize', this.defaultSetting.titleFontSize);
    settingAsMap.set('yAxis[0].title.style.color', this.defaultSetting.titleColor);
    if (this.enableGridSetting) {
      settingAsMap.set('yAxis[0].gridLineWidth', this.defaultSetting.gridLineWidth);
      settingAsMap.set('yAxis[0].gridLineColor', this.defaultSetting.gridLineColor);
      settingAsMap.set('yAxis[0].gridLineDashStyle', this.defaultSetting.gridLineDashStyle);
    }
    settingAsMap.set('yAxis[0].prefix.text', this.defaultSetting.prefixText);
    settingAsMap.set('yAxis[0].postfix.text', this.defaultSetting.postfixText);
    settingAsMap.set('yAxis[0].condition.enabled', false);
    settingAsMap.set('yAxis[0].condition.min.value', this.defaultSetting.min);
    settingAsMap.set('yAxis[0].condition.min.enabled', false);
    settingAsMap.set('yAxis[0].condition.max.value', this.defaultSetting.max);
    settingAsMap.set('yAxis[0].condition.max.enabled', false);
    this.$emit('onMultipleChanged', settingAsMap);
  }

  private handleGridLineWidthChanged(newWidth: number) {
    if (this.gridEnabled) {
      return this.$emit('onChanged', 'yAxis[0].gridLineWidth', newWidth);
    }
  }

  private handleGridColorChanged(newColor: string) {
    return this.$emit('onChanged', 'yAxis[0].gridLineColor', newColor);
  }

  private handleGridLineDashStyleChanged(newDashStyle: string) {
    return this.$emit('onChanged', 'yAxis[0].gridLineDashStyle', newDashStyle);
  }

  private handleConditionChanged(enable: boolean) {
    this.$emit('onChanged', 'yAxis[0].condition.enabled', enable);
  }

  private handleMinConditionChanged(enable: boolean) {
    this.$emit('onChanged', 'yAxis[0].condition.min.enabled', enable);
    if (enable) {
      this.handleMinValueChanged(this.minYAxis);
    }
  }

  private handleMaxConditionChanged(enable: boolean) {
    this.$emit('onChanged', 'yAxis[0].condition.max.enabled', enable);
    if (enable) {
      this.handleMaxValueChanged(this.maxYAxis);
    }
  }

  private handleMinValueChanged(value: string) {
    this.$emit('onChanged', 'yAxis[0].condition.min.value', +value);
  }

  private handleMaxValueChanged(value: string) {
    this.$emit('onChanged', 'yAxis[0].condition.max.value', +value);
  }

  private handlePrefixSaved(newText: string) {
    return this.$emit('onChanged', 'yAxis[0].prefix.text', newText);
  }

  private handlePostfixSaved(newText: string) {
    return this.$emit('onChanged', 'yAxis[0].postfix.text', newText);
  }
}
</script>

<style lang="scss" scoped></style>
