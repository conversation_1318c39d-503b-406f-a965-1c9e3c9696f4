:root,
  // for builder
body div[builder-default-style] {
  // mau cua root, chi root moi duoc override mau nay, khong duoc override mau nay ben duoi
  --primary--root: #f2f2f7;
  --text-color--root: #4f4f4f;
  --secondary-text-color--root: #5f6368;
  --hover-color--root: #f9faff;
  --secondary--root: #ffffff;
  --accent--root: #597fff;

  // default color, co the override cac variable nay
  --primary: #f2f2f7;
  --secondary: #ffffff;
  --secondary-2: #fafafb;
  --active-color: #597fff0d;
  --hover-color: #f9faff;
  --panel-filter-color: #fff;
  --filter-color: #fff;
  --editor-color: var(--input-background-color);
  --header-background-color: #f5f5f5;
  --table-header-color: #5f6368;
  --row-even-background-color: #00000000;
  --row-even-color: #5f6368;
  --row-odd-background-color: #00000003;
  --row-odd-color: #5f6368;
  --table-page-active-color: #fff;
  --tooltip-background-color: #f2f2f7;
  --tooltip-color: #5f6368;
  --total-background-color: #00000003;
  --directory-header-bg: #fafafb;
  --directory-row-bg: #fff;
  --directory-color: #ffffff;
  --text-color: #4f4f4f;
  --secondary-text-color: #5f6368;
  --menu-background-color: #fff;
  --menu-shadow: 0 4px 4px 0 #00000014;
  --menu-shadow--root: 0 4px 4px 0 #00000014;
  --menu-border: unset;
  --directory-action-color: #000;
  --icon-color: #5f6368;
  --icon-background-color: #fff;
  --directory-hover-color: var(--hover-color);
  --directory-grid-line-color: #0000000f;
  --directory-header-icon-color: #5f6368;
  --modal-background-color: var(--secondary);
  --input-background-color: var(--primary);
  --header-text-color: #fff;
  --header-active-background-color: #fff;
  --header-icon-color: #fff;
  --header-icon-active-color: var(--accent);
  --header-text-active-color: var(--accent);
  --table-grid-line-color: #0000000a;
  --charcoal: #00000033;
  --panel-background-color: #fff;
  --relationship-border: 2px dashed rgba(0, 0, 0, 0.3);
  --relationship-db-color: rgba(0, 0, 0, 0.4);
  --relationship-table-item-bg: #fff;
  --data-source-item-bg: #fafafb;
  --data-source-item-border: solid 1px #f2f2f7;
  --chart-icon-bg: var(--primary);
  --chart-icon-small-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  --chart-icon-small-bg: #fff;
  --config-draggable-bg: #f2f2f7;
  --config-draggable-border: solid 1.5px #f2f2f7;
  --builder-font-weight: 500;
  --chart-icon-listing-bg: #f2f2f7;
  --suspend-status-color: #ea6b6b;
  --active-status-color: #07bc40;
  --header-color: #597fff;
  --transparent: #00000000;
  --neutral: #9799ac;
  --white: #fff;
  --dark: #2b2b2b;
  --blue-grey: #9799ad;
  --text-color-button: #ffffff80;
  --accent: #597fff;
  --accent-text-color: #fff;
  --purple: #c797ff;
  --yellow: #ffe659;
  --success: #07bc40;
  --warning: #ffc107;
  --danger: #ea6b6b;
  --backdrop: #131317;
  --light-gold: #ffe659;
  --heatmap-min: #ffffff;
  --heatmap-max: #c4463a;
  --grid-line-color: #00000019;
  --table-border-color: #505362;
  --disable-alpha: 0.3;
  --enable-alpha: 1;
  --disable-opacity: 0.4;
  --enable-opacity: 1;
  --user-profile-table-background-color: #2d303c;
  --user-profile-background-color: #2d303c;
  --number-decrease-color: #f7517f;
  --number-increase-color: #16ceb9;
  --copper: #cc4426;
  --slate: #4a506a;
  --th-background-color: #4a506a;
  --td-odd-background-color: #2f3240;
  --normal-opacity: 0.5;
  --active-opacity: 1;
  --scrollbar-background: #d6d6d6;
  --scrollbar-hover: #5f6368;
  --toggle-color: #f2f2f7;
  --tab-filter-background-active: var(--accent);
  --tab-filter-background-de-active: var(--white);
  --choice-filter-background-active: var(--accent);
  --choice-filter-background-de-active: #9799ac;
  --tab-filter-dropdown-background: var(--primary);
  --tab-filter-active: var(--white);
  --tab-filter-de-active: var(--text-color);
  --grid-horizontal-color: #0000000f;
  --calendar-bg: #ffffff;
  ///New palette color
  --white2: #ffffff;
  --black2: #4f4f4f;
  --grey2: #5f6368;
  --background2: #f2f2f7;
  --accent-2: #0066ff;
  --line-f0f0f0: #f0f0f0;
  --line-d6d6d6: #d6d6d6;
  --error: #de3618;
}
