// see more option in [file](~@/themes/scss/_toast.scss);
@import '~@/themes/scss/mixin.scss';
$toast-header-background-color: transparent;
$toast-border-width: 0;
$toast-border-color: 0;
$toast-box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
$toast-max-width: 410px;

.b-toaster-slot {
  max-width: 442px !important;
}

.b-toast {
  max-width: 410px !important;
}

.b-toast-danger {
  background-color: white;
  color: var(--text-color);
  border-radius: 8px;
  margin: 1rem;

  .toast-header {
    padding: 1rem 1rem 0.35rem 1rem;
    width: 100% !important;

    @include bold-text();
    font-size: 1rem;
    strong {
      letter-spacing: 0.27px;
      color: var(--copper);
    }
    button {
      color: var(--text-color);
    }
  }

  .toast-body {
    padding: 0.35rem 1rem 1rem 1rem;
    word-break: break-word;
    @include regular-text();
    font-size: 1rem;
    letter-spacing: 0.27px;
    opacity: 0.5;
  }
}

.b-toast-success {
  background-color: white;
  border-radius: 8px;
  margin: 1rem;
  color: var(--text-color);

  .toast-header {
    padding: 1rem 1rem 0.35rem 1rem;
    @include bold-text();
    font-size: 1rem;
    strong {
      letter-spacing: 0.27px;
      color: var(--success);
    }
    button {
      color: var(--text-color);
    }
  }

  .toast-body {
    padding: 0.35rem 1rem 1rem 1rem;
    word-break: break-word;
    @include regular-text();
    font-size: 1rem;
    letter-spacing: 0.27px;
    opacity: 0.5;
  }
}
