@keyframes lds-grid {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.lds-grid {
  display: inline-block;
  position: relative;
  width: 40px;
  height: 40px;

  div {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--neutral);
    animation: lds-grid 1.2s linear infinite;

    &:nth-child(1) {
      top: 4px;
      left: 4px;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      top: 4px;
      left: 16px;
      animation-delay: -0.4s;
    }

    &:nth-child(3) {
      top: 4px;
      left: 28px;
      animation-delay: -0.8s;
    }

    &:nth-child(4) {
      top: 16px;
      left: 4px;
      animation-delay: -0.4s;
    }

    &:nth-child(5) {
      top: 16px;
      left: 16px;
      animation-delay: -0.8s;
    }

    &:nth-child(6) {
      top: 16px;
      left: 28px;
      animation-delay: -1.2s;
    }

    &:nth-child(7) {
      top: 28px;
      left: 4px;
      animation-delay: -0.8s;
    }

    &:nth-child(8) {
      top: 28px;
      left: 16px;
      animation-delay: -1.2s;
    }

    &:nth-child(9) {
      top: 28px;
      left: 28px;
      animation-delay: -1.6s;
    }
  }
}
