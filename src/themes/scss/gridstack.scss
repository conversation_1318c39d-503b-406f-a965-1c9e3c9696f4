/*!
 * gridstack 2.1.0 required CSS for default 48 and 1 column Mode size. Use gridstack-extra.css for column [2-11], else see https://github.com/gridstack/gridstack.js#custom-columns-css
 * https://gridstackjs.com/
 * (c) 2014-2020 <PERSON>, <PERSON>, <PERSON>
 * gridstack.js may be freely distributed under the MIT license.
 *
 * @customized by @tvc12
 */
:root .grid-stack-item > .ui-resizable-handle {
  filter: none;
}

.grid-stack {
  position: relative;
}

.grid-stack.grid-stack-rtl {
  direction: ltr;
}

.grid-stack.grid-stack-rtl > .grid-stack-item {
  direction: rtl;
}

.grid-stack .grid-stack-placeholder > .placeholder-content {
  border: 1px dashed lightgray;
  margin: 0;
  position: absolute;
  width: auto;
  z-index: 0 !important;
  text-align: center;
}

.grid-stack > .grid-stack-item {
  min-width: 8.**********%;
  position: absolute;
  padding: 0;
}

.grid-stack > .grid-stack-item > .grid-stack-item-content {
  margin: 0;
  position: absolute;
  width: auto;
  overflow-x: hidden;
  overflow-y: auto;
}

.grid-stack > .grid-stack-item > .ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}

.grid-stack > .grid-stack-item.ui-resizable-disabled > .ui-resizable-handle,
.grid-stack > .grid-stack-item.ui-resizable-autohide > .ui-resizable-handle {
  display: none;
}

.grid-stack > .grid-stack-item.ui-draggable-dragging,
.grid-stack > .grid-stack-item.ui-resizable-resizing {
  z-index: 100;
}

.grid-stack > .grid-stack-item.ui-draggable-dragging > .grid-stack-item-content,
.grid-stack > .grid-stack-item.ui-draggable-dragging > .grid-stack-item-content,
.grid-stack > .grid-stack-item.ui-resizable-resizing > .grid-stack-item-content,
.grid-stack > .grid-stack-item.ui-resizable-resizing > .grid-stack-item-content {
  //box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
  //background: transparent;
}

.grid-stack > .grid-stack-item > .ui-resizable-se,
.grid-stack > .grid-stack-item > .ui-resizable-sw {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pgo8IS0tIEdlbmVyYXRvcjogQWRvYmUgSWxsdXN0cmF0b3IgMTYuMC4wLCBTVkcgRXhwb3J0IFBsdWctSW4gLiBTVkcgVmVyc2lvbjogNi4wMCBCdWlsZCAwKSAgLS0+CjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+CjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgdmVyc2lvbj0iMS4xIiBpZD0iQ2FwYV8xIiB4PSIwcHgiIHk9IjBweCIgd2lkdGg9IjE2cHgiIGhlaWdodD0iMTZweCIgdmlld0JveD0iMCAwIDUxMS42MjYgNTExLjYyNyIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTExLjYyNiA1MTEuNjI3OyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+CjxnPgoJPHBhdGggZD0iTTMyOC45MDYsNDAxLjk5NGgtMzYuNTUzVjEwOS42MzZoMzYuNTUzYzQuOTQ4LDAsOS4yMzYtMS44MDksMTIuODQ3LTUuNDI2YzMuNjEzLTMuNjE1LDUuNDIxLTcuODk4LDUuNDIxLTEyLjg0NSAgIGMwLTQuOTQ5LTEuODAxLTkuMjMxLTUuNDI4LTEyLjg1MWwtNzMuMDg3LTczLjA5QzI2NS4wNDQsMS44MDksMjYwLjc2LDAsMjU1LjgxMywwYy00Ljk0OCwwLTkuMjI5LDEuODA5LTEyLjg0Nyw1LjQyNCAgIGwtNzMuMDg4LDczLjA5Yy0zLjYxOCwzLjYxOS01LjQyNCw3LjkwMi01LjQyNCwxMi44NTFjMCw0Ljk0NiwxLjgwNyw5LjIyOSw1LjQyNCwxMi44NDVjMy42MTksMy42MTcsNy45MDEsNS40MjYsMTIuODUsNS40MjYgICBoMzYuNTQ1djI5Mi4zNThoLTM2LjU0MmMtNC45NTIsMC05LjIzNSwxLjgwOC0xMi44NSw1LjQyMWMtMy42MTcsMy42MjEtNS40MjQsNy45MDUtNS40MjQsMTIuODU0ICAgYzAsNC45NDUsMS44MDcsOS4yMjcsNS40MjQsMTIuODQ3bDczLjA4OSw3My4wODhjMy42MTcsMy42MTcsNy44OTgsNS40MjQsMTIuODQ3LDUuNDI0YzQuOTUsMCw5LjIzNC0xLjgwNywxMi44NDktNS40MjQgICBsNzMuMDg3LTczLjA4OGMzLjYxMy0zLjYyLDUuNDIxLTcuOTAxLDUuNDIxLTEyLjg0N2MwLTQuOTQ4LTEuODA4LTkuMjMyLTUuNDIxLTEyLjg1NCAgIEMzMzguMTQyLDQwMy44MDIsMzMzLjg1Nyw0MDEuOTk0LDMyOC45MDYsNDAxLjk5NHoiIGZpbGw9IiM2NjY2NjYiLz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8Zz4KPC9nPgo8L3N2Zz4K);
  background-repeat: no-repeat;
  background-position: center;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}

.grid-stack > .grid-stack-item > .ui-resizable-se {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

// hide icon
.ui-resizable-se,
.ui-resizable-sw {
  opacity: 0 !important;
}

.ui-resizable-se {
  bottom: -5px !important;
  right: -5px !important;
}

.grid-stack > .grid-stack-item > .ui-resizable-nw {
  cursor: nw-resize;
  width: 20px;
  height: 20px;
  top: 0;
}

.grid-stack > .grid-stack-item > .ui-resizable-n {
  cursor: n-resize;
  height: 10px;
  top: 0;
  left: 25px;
  right: 25px;
}

.grid-stack > .grid-stack-item > .ui-resizable-ne {
  cursor: ne-resize;
  width: 20px;
  height: 20px;
  top: 0;
}

.grid-stack > .grid-stack-item > .ui-resizable-e {
  cursor: e-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}

.grid-stack > .grid-stack-item > .ui-resizable-se {
  cursor: se-resize;
  width: 20px;
  height: 20px;
}

.grid-stack > .grid-stack-item > .ui-resizable-s {
  cursor: s-resize;
  height: 10px;
  left: 25px;
  bottom: 0;
  right: 25px;
}

.grid-stack > .grid-stack-item > .ui-resizable-sw {
  cursor: sw-resize;
  width: 20px;
  height: 20px;
  bottom: 0;
}

.grid-stack > .grid-stack-item > .ui-resizable-w {
  cursor: w-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}

.grid-stack > .grid-stack-item.ui-draggable-dragging > .ui-resizable-handle {
  display: none !important;
}

.grid-stack > .grid-stack-item {
  $grid-columns: 48;

  @function fixed($float) {
    @return round($float * 1000) / 1000; // total 2+3 digits being %
  }

  min-width: fixed(100% / $grid-columns);

  @for $i from 1 through $grid-columns {
    &[data-gs-width='#{$i}'] {
      width: fixed((100% / $grid-columns) * $i);
    }
    &[data-gs-x='#{$i}'] {
      left: fixed((100% / $grid-columns) * $i);
    }
    &[data-gs-min-width='#{$i}'] {
      min-width: fixed((100% / $grid-columns) * $i);
    }
    &[data-gs-max-width='#{$i}'] {
      max-width: fixed((100% / $grid-columns) * $i);
    }
  }
}

.grid-stack.grid-stack-1 > .grid-stack-item {
  min-width: 100%;
}

.grid-stack.grid-stack-1 > .grid-stack-item[data-gs-width='1'] {
  width: 100%;
}

.grid-stack.grid-stack-1 > .grid-stack-item[data-gs-x='1'] {
  left: 100%;
}

.grid-stack.grid-stack-1 > .grid-stack-item[data-gs-min-width='1'] {
  min-width: 100%;
}

.grid-stack.grid-stack-1 > .grid-stack-item[data-gs-max-width='1'] {
  max-width: 100%;
}

.grid-stack.grid-stack-animate,
.grid-stack.grid-stack-animate .grid-stack-item {
  -webkit-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -moz-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -ms-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  -o-transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
  transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
}

.grid-stack.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
.grid-stack.grid-stack-animate .grid-stack-item.ui-resizable-resizing,
.grid-stack.grid-stack-animate .grid-stack-item.grid-stack-placeholder {
  -webkit-transition: left 0s, top 0s, height 0s, width 0s;
  -moz-transition: left 0s, top 0s, height 0s, width 0s;
  -ms-transition: left 0s, top 0s, height 0s, width 0s;
  -o-transition: left 0s, top 0s, height 0s, width 0s;
  transition: left 0s, top 0s, height 0s, width 0s;
}

.grid-stack-item-content {
  //border-radius: 4px;
  //background-color: var(--secondary);
  overflow: visible !important;
}
