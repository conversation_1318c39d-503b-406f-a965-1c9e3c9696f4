//--------------- Padding Left Right -----------------//

.pad-x-5 {
  padding: 0 5px;
}

.pad-x-12 {
  padding: 0 12px;
}

.pad-x-15 {
  padding: 0 15px;
}

.pad-x-24 {
  padding: 0 24px;
}

.pad-x-32 {
  padding: 0 32px;
}

// --------------- Padding Top Bottom -----------------//

.pad-y-4 {
  padding-top: 4px;
  padding-bottom: 4px;
}

.pad-y-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.pad-y-12 {
  padding-top: 12px;
  padding-bottom: 12px;
}

.pad-y-16 {
  padding-top: 16px;
  padding-bottom: 16px;
}

.pad-y-15 {
  padding: 15px 0;
}

// --------------- Padding All -----------------//

.pad-4 {
  padding: 4px;
}

.pad-8 {
  padding: 8px;
}

.pad-12 {
  padding: 12px;
}

.pad-15 {
  padding: 15px;
}

.pad-16 {
  padding: 16px;
}

// --------------- Padding Right -----------------//

.pad-r-4 {
  padding-right: 4px;
}

.pad-r-8 {
  padding-right: 8px;
}

.pad-r-16 {
  padding-right: 16px !important;
}

.pr-24 {
  padding-right: 24px !important;
}

// --------------- Padding Left -----------------//

.pad-l-4 {
  padding-left: 4px;
}

.pad-l-8 {
  padding-left: 8px;
}

pad-l-15 {
  padding-left: 15px;
}

.pad-l-16 {
  padding-left: 16px;
}

.pad-l-24 {
  padding-left: 24px;
}

.pl-24 {
  padding-left: 24px !important;
}

// --------------- Margin Bottom -----------------//

.mar-b-4 {
  margin-bottom: 4px;
}

.mar-b-8 {
  margin-bottom: 8px;
}

.mar-b-12 {
  margin-bottom: 12px;
}

.mar-b-16 {
  margin-bottom: 16px;
}

.mar-b-22 {
  margin-bottom: 22px;
}

.mar-b-24 {
  margin-bottom: 24px;
}

.mar-b-32 {
  margin-bottom: 32px;
}

.mar-b-48 {
  margin-bottom: 48px;
}

// --------------- Margin Right -----------------//

.mar-r-4 {
  margin-right: 4px;
}

.mar-r-8 {
  margin-right: 8px;
}

.mar-r-12 {
  margin-right: 12px;
}

.mar-r-16 {
  margin-right: 16px;
}

.mar-r-22 {
  margin-right: 22px;
}

.mar-r-24 {
  margin-right: 24px;
}

// --------------- Margin Top -----------------//

.mar-t-4 {
  margin-top: 4px;
}

.mar-t-8 {
  margin-top: 8px;
}

.mar-t-12 {
  margin-top: 12px;
}

.mar-t-32 {
  margin-top: 32px;
}

.mar-t-24 {
  margin-top: 24px;
}

.mar-t-15 {
  margin-top: 15px;
}

// --------------- Margin Top Bottom -----------------//

.mar-y-4 {
  margin-top: 4px;
  margin-bottom: 4px;
}

.mar-y-8 {
  margin-top: 8px;
  margin-bottom: 8px;
}

.mar-y-15 {
  padding: 15px 0;
}

// ------------------------------------------//

img[class~='ic-16'] {
  @include ic-16;
}

img[class~='ic-32'] {
  @include ic-32;
}

img[class~='ic-40'] {
  @include ic-40;
}

.text-14px {
  font-size: 14px !important;
}

.text-12px {
  font-size: 12px !important;
}

.w-60 {
  width: 60% !important;
}

.w-70 {
  width: 70% !important;
}

.h-42px {
  height: 42px !important;
}

.h-44px {
  height: 44px !important;
}

.opacity-0dot5 {
  opacity: 0.5;
}

.regular-icon-16 {
  @include regular-text;
  font-size: 14px;
  letter-spacing: 0.2px;
  white-space: nowrap;
  padding: 4px;
}

.regular-text-24 {
  @include regular-text;
  font-size: 24px;
  letter-spacing: 0.2px;
  opacity: 1;
  white-space: nowrap;
  margin-left: 16px;
  line-height: 1.4;
  vertical-align: middle;
}

.regular-text-14-white {
  @include regular-text();
  font-size: 14px;
  line-height: unset;
  opacity: 1 !important;
  text-align: left;
  white-space: nowrap;
  width: 100%;
}

//Wrap p with div and set width to it for using
.single-line {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.horizontal-scroll {
  overflow-x: scroll;
}

.vertical-scroll {
  overflow-y: scroll;
}

.unselectable,
img {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Old versions of Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome, Edge, Opera and Firefox */
}

.non-interactive {
  pointer-events: none;
}

svg[icon-size='24'] {
  width: 24px;
  height: 24px;
}

svg[icon-size='16'] {
  width: 16px;
  height: 16px;
}

svg[icon-size='60'] {
  width: 60px;
  height: 60px;
}

.icon-title {
  font-size: 16px;
  width: 16px;
  height: 16px;
  object-fit: contain;
}
